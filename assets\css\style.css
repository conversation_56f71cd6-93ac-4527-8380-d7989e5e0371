/* تصميم عام للنظام */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    border-radius: 0.375rem;
    margin: 0.25rem 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #ffae00;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

/* المحتوى الرئيسي */
main {
    margin-top: 56px;
    padding-top: 20px;
}

/* بطاقات المنتجات */
.product-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.product-card:hover {
    border-color: #007bff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-2px);
}

.product-card .product-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-card .product-price {
    color: #28a745;
    font-weight: 700;
    font-size: 1.1rem;
}

.product-card .product-stock {
    color: #6c757d;
    font-size: 0.9rem;
}

/* جدول الفاتورة */
.invoice-items {
    max-height: 300px;
    overflow-y: auto;
}

.invoice-items table {
    margin-bottom: 0;
}

.invoice-items .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* أزرار العمليات */
.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

/* تصميم responsive */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-top: 0;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
}

/* تحسينات للفواتير المعلقة */
.held-invoice-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.held-invoice-item:hover {
    border-color: #007bff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.held-invoice-item .invoice-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.held-invoice-item .invoice-details {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* تحسينات للبحث */
#product-search {
    border-radius: 0.375rem;
    border: 2px solid #dee2e6;
    transition: border-color 0.3s ease;
}

#product-search:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات للإجماليات */
.border-top {
    border-top: 2px solid #dee2e6 !important;
}

/* تحسينات للأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* تحسينات للبطاقات */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* تحسينات للجداول */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

/* تحسينات للنماذج */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات للشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 0.5rem;
    border: none;
}

/* تحسينات للمودال */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* تحسينات للتحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات للرسائل */
.toast {
    border-radius: 0.5rem;
}

/* تحسينات للأيقونات */
.fas, .far {
    width: 16px;
    text-align: center;
}

/* تحسينات للنصوص */
.text-muted {
    color: #6c757d !important;
}

.fw-bold {
    font-weight: 700 !important;
}

/* تحسينات للمسافات */
.me-1 { margin-left: 0.25rem !important; }
.me-2 { margin-left: 0.5rem !important; }
.me-3 { margin-left: 1rem !important; }
.ms-auto { margin-right: auto !important; }

/* تحسينات للألوان */
.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-info { background-color: #17a2b8 !important; }

.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #17a2b8 !important; }
