# نظام نقاط البيع (POS System)

نظام شامل لإدارة نقاط البيع يعمل عبر المتصفح، مصمم خصيصاً للشركات الصغيرة والمتوسطة.

## 🌟 المميزات الرئيسية

### 📱 شاشة نقاط البيع (POS)
- ✅ البحث عن المنتجات بالاسم أو الباركود
- ✅ إدارة الفواتير والمبيعات
- ✅ **ميزة تعليق الفاتورة (Hold Invoice)** - إمكانية حفظ الفواتير مؤقتاً والعودة إليها لاحقاً
- ✅ اختيار العملاء (نقدي أو مسجل)
- ✅ طرق دفع متعددة (نقدي، آجل)
- ✅ نظام خصومات مرن
- ✅ طباعة الفواتير

### 👥 إدارة العملاء والموردين
- إضافة وتعديل بيانات العملاء
- متابعة حسابات العملاء والديون
- إدارة بيانات الموردين
- تتبع المدفوعات والمستحقات

### 📊 لوحة تحكم المدير
- إحصائيات المبيعات اليومية
- تقارير شاملة (يومية، أسبوعية، شهرية)
- متابعة أداء العملاء والموردين
- إدارة المخزون والتنبيهات

### 📦 إدارة المخزون
- متابعة الكميات المتاحة
- تنبيهات عند انخفاض المخزون
- تتبع حركات المخزون
- إدارة فئات المنتجات

## 🛠️ التقنيات المستخدمة

- **الواجهة الأمامية:** HTML5, CSS3, JavaScript, Bootstrap 5
- **الخلفية:** PHP 7.4+
- **قاعدة البيانات:** MySQL 5.7+
- **الأيقونات:** Font Awesome 6
- **التصميم:** Bootstrap 5 مع دعم RTL

## 📋 متطلبات النظام

- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- امتداد PDO MySQL
- XAMPP/WAMP/MAMP (للتطوير المحلي)

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd pos-system
```

### 2. إعداد خادم الويب
- ضع ملفات المشروع في مجلد الويب (htdocs في XAMPP)
- تأكد من تشغيل Apache و MySQL

### 3. إعداد قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost/pos-system/setup_database.php`
2. اتبع التعليمات لإعداد قاعدة البيانات
3. سيتم إنشاء قاعدة البيانات والجداول تلقائياً

### 4. تسجيل الدخول
- اذهب إلى: `http://localhost/pos-system/`
- استخدم بيانات تسجيل الدخول الافتراضية:
  - **اسم المستخدم:** admin
  - **كلمة المرور:** admin123

## 🎯 ميزة تعليق الفاتورة (Hold Invoice)

### كيفية الاستخدام:
1. **إنشاء فاتورة:** أضف المنتجات للفاتورة كالمعتاد
2. **تعليق الفاتورة:** اضغط على زر "تعليق الفاتورة" 
3. **إنشاء فاتورة جديدة:** سيتم إنشاء فاتورة جديدة تلقائياً
4. **استكمال الفاتورة المعلقة:** اضغط على "الفواتير المعلقة" واختر الفاتورة المطلوبة

### المميزات:
- ✅ حفظ تلقائي لجميع بيانات الفاتورة
- ✅ إمكانية تعليق عدة فواتير في نفس الوقت
- ✅ عرض تفاصيل كاملة للفواتير المعلقة
- ✅ حفظ معلومات العميل وطريقة الدفع
- ✅ إمكانية حذف الفواتير المعلقة غير المرغوبة

## 📁 هيكل المشروع

```
pos-system/
├── index.html              # الصفحة الرئيسية
├── setup_database.php      # إعداد قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css       # ملف التصميم
│   └── js/
│       └── app.js          # الوظائف الرئيسية
├── config/
│   └── database.php        # إعدادات قاعدة البيانات
├── api/
│   └── held_invoices.php   # API الفواتير المعلقة
└── README.md               # هذا الملف
```

## 🔧 إعدادات قاعدة البيانات

يمكنك تعديل إعدادات قاعدة البيانات في ملف `config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'pos_system');
define('DB_USER', 'root');
define('DB_PASS', '');
```

## 📊 جداول قاعدة البيانات

- `users` - المستخدمين
- `customers` - العملاء
- `suppliers` - الموردين
- `categories` - فئات المنتجات
- `products` - المنتجات
- `invoices` - الفواتير
- `invoice_items` - عناصر الفواتير
- `held_invoices` - الفواتير المعلقة ⭐
- `payments` - المدفوعات
- `stock_movements` - حركات المخزون

## 🔐 الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- استخدام Prepared Statements لمنع SQL Injection
- التحقق من صحة البيانات المدخلة
- نظام أذونات للمستخدمين

## 🐛 استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات:
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات الاتصال في `config/database.php`
3. تأكد من وجود قاعدة البيانات `pos_system`

### مشكلة في عرض الصفحات:
1. تأكد من تشغيل Apache
2. تحقق من مسار الملفات
3. تأكد من أذونات الملفات

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راجع ملف التوثيق
- تحقق من سجلات الأخطاء

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🔄 التحديثات المستقبلية

- [ ] دعم الطابعات الحرارية
- [ ] تطبيق جوال
- [ ] تقارير متقدمة
- [ ] نظام النسخ الاحتياطي
- [ ] دعم عدة فروع
- [ ] تكامل مع أنظمة المحاسبة

---

**نظام نقاط البيع - الإصدار 1.0**  
تم التطوير بواسطة فريق التطوير المحلي
