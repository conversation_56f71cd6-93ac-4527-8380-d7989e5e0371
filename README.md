# نظام نقاط البيع (POS System) 🛒

نظام نقاط بيع شامل ومتطور يعمل عبر المتصفح، مصمم خصيصاً للشركات الصغيرة والمتوسطة مع دعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### 🛒 شاشة نقاط البيع (POS)
- ✅ البحث عن المنتجات بالاسم أو الباركود
- ✅ إدارة الفواتير والعملاء بسهولة
- ✅ دعم طرق الدفع المختلفة (نقدي / آجل / بطاقة ائتمان)
- ✅ إضافة خصومات على الفاتورة أو الأصناف المحددة
- ⭐ **ميزة تعليق الفاتورة (Hold Invoice)** - للتعامل مع عدة عملاء بنفس الوقت
- ✅ طباعة الفواتير للطابعات الحرارية والعادية
- ✅ آلة حاسبة مدمجة
- ✅ اختصارات لوحة المفاتيح للسرعة

### 👥 إدارة العملاء والموردين
- ✅ إضافة وتعديل بيانات العملاء والموردين
- ✅ متابعة الديون والمدفوعات
- ✅ تسجيل المدفوعات وتتبع الأرصدة
- ✅ إحصائيات شاملة للعملاء
- ✅ تقارير العملاء والموردين

### 📊 لوحة تحكم المدير والتقارير
- ✅ إحصائيات المبيعات اليومية والشهرية
- ✅ رسوم بيانية تفاعلية (Chart.js)
- ✅ تقارير شاملة (مبيعات، عملاء، منتجات، مخزون)
- ✅ فلاتر متقدمة للتقارير
- ✅ إمكانية تصدير التقارير
- ✅ متابعة الأداء والإحصائيات

### 📦 إدارة المخزون
- ✅ إضافة وتعديل المنتجات والفئات
- ✅ متابعة مستويات المخزون
- ✅ تنبيهات المخزون المنخفض
- ✅ إدارة الموردين والفئات
- ✅ تتبع حركة المخزون
- ✅ تقارير المخزون المفصلة

### 🖨️ نظام الطباعة المتقدم
- ✅ طباعة فواتير احترافية
- ✅ دعم الطابعات الحرارية (POS Printers)
- ✅ تخطيط مخصص للفواتير
- ✅ طباعة التقارير اليومية
- ✅ تصميم متجاوب للطباعة

## 🛠️ التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Bootstrap 5** - إطار العمل مع دعم RTL
- **Font Awesome 6** - الأيقونات
- **Chart.js** - الرسوم البيانية

### Backend
- **PHP 7.4+** - لغة البرمجة الخلفية
- **PDO** - للتعامل مع قاعدة البيانات
- **RESTful API** - تصميم API متقدم

### Database
- **MySQL 5.7+** - قاعدة البيانات
- **10 جداول** - تصميم قاعدة بيانات شامل
- **Foreign Keys** - علاقات متقدمة

## 📋 متطلبات التشغيل

- **XAMPP/WAMP/LAMP** - بيئة التطوير
- **PHP 7.4+** - إصدار PHP
- **MySQL 5.7+** - قاعدة البيانات
- **متصفح ويب حديث** - Chrome, Firefox, Safari, Edge

## 🚀 طريقة التثبيت

### 1. تحضير البيئة
```bash
# تحميل وتثبيت XAMPP
# تشغيل Apache و MySQL من لوحة تحكم XAMPP
```

### 2. نسخ الملفات
```bash
# نسخ مجلد المشروع إلى htdocs
C:\xampp\htdocs\نقاط بيع\
```

### 3. إعداد قاعدة البيانات
1. فتح المتصفح والذهاب إلى: `http://localhost/نقاط بيع/setup_database.php`
2. الضغط على "بدء إعداد قاعدة البيانات"
3. انتظار اكتمال الإعداد

### 4. البدء في الاستخدام
- الذهاب إلى: `http://localhost/نقاط بيع/`
- البدء في استخدام النظام

## 📖 دليل الاستخدام

### ⭐ ميزة تعليق الفاتورة (Hold Invoice)
هذه الميزة الأساسية تسمح للكاشير بإدارة عدة عملاء في نفس الوقت:

1. **إضافة منتجات** للفاتورة الحالية
2. **الضغط على "تعليق الفاتورة"** أو استخدام **F2**
3. **بدء فاتورة جديدة** لعميل آخر
4. **الضغط على "الفواتير المعلقة"** أو **F3** لاستكمال الفواتير السابقة
5. **اختيار "استكمال"** للعودة لفاتورة معلقة

### ⌨️ اختصارات لوحة المفاتيح
| المفتاح | الوظيفة |
|---------|---------|
| **F1** | فاتورة جديدة |
| **F2** | تعليق الفاتورة |
| **F3** | عرض الفواتير المعلقة |
| **F4** | إتمام البيع |
| **F5** | طباعة الفاتورة |
| **F9** | آلة حاسبة |
| **Ctrl+F** | البحث في المنتجات |
| **Escape** | إغلاق النوافذ المنبثقة |

### 🔍 البحث والفلترة
- **البحث بالاسم**: كتابة اسم المنتج
- **البحث بالباركود**: مسح أو كتابة الباركود
- **فلترة العملاء**: حسب الحالة والديون
- **فلترة المخزون**: حسب الفئة والحالة

## 📁 هيكل المشروع

```
نقاط بيع/
├── 📄 index.html                    # الواجهة الرئيسية
├── 🔧 setup_database.php            # إعداد قاعدة البيانات
├── 📁 config/
│   └── 🔗 database.php              # إعدادات قاعدة البيانات
├── 📁 api/
│   ├── 🛍️ products.php              # API المنتجات
│   ├── 👤 customers.php             # API العملاء
│   ├── 🏢 suppliers.php             # API الموردين
│   ├── 📂 categories.php            # API الفئات
│   └── 📋 held_invoices.php         # API الفواتير المعلقة
├── 📁 assets/
│   ├── 📁 css/
│   │   └── 🎨 style.css             # التصميم الشامل
│   └── 📁 js/
│       ├── ⚙️ app.js                # الوظائف الرئيسية + POS
│       ├── 👥 customers.js          # إدارة العملاء
│       ├── 🏭 suppliers.js          # إدارة الموردين
│       ├── 📦 inventory.js          # إدارة المخزون
│       └── 📊 reports.js            # التقارير والإحصائيات
└── 📖 README.md                     # هذا الملف
```

## 🗃️ قاعدة البيانات

### الجداول الرئيسية:
1. **users** - المستخدمين
2. **customers** - العملاء
3. **suppliers** - الموردين
4. **categories** - فئات المنتجات
5. **products** - المنتجات
6. **invoices** - الفواتير
7. **invoice_items** - عناصر الفواتير
8. **held_invoices** - الفواتير المعلقة
9. **payments** - المدفوعات
10. **stock_movements** - حركة المخزون

## 🎯 الميزات المتقدمة

### 💾 نظام الحفظ المزدوج
- **حفظ في قاعدة البيانات** كأولوية
- **حفظ محلي** كنسخة احتياطية
- **مزامنة تلقائية** عند استعادة الاتصال

### 🔄 التحديث التلقائي
- **تحديث الإحصائيات** في الوقت الفعلي
- **تحديث المخزون** تلقائياً
- **تحديث أرصدة العملاء** فورياً

### 📱 التصميم المتجاوب
- **دعم الهواتف الذكية** والأجهزة اللوحية
- **واجهة عربية** كاملة مع دعم RTL
- **تصميم حديث** وسهل الاستخدام

## 🤝 المساهمة

نرحب بالمساهمات من المطورين! للمساهمة:

1. **Fork** المشروع
2. إنشاء **branch جديد** للميزة
3. **إضافة التحسينات** والاختبار
4. إرسال **Pull Request** مع وصف مفصل

### مجالات المساهمة:
- 🐛 إصلاح الأخطاء
- ✨ إضافة ميزات جديدة
- 📚 تحسين التوثيق
- 🎨 تحسين التصميم
- 🔧 تحسين الأداء

## 📄 الترخيص

هذا المشروع **مفتوح المصدر** ومتاح للاستخدام:
- ✅ **الاستخدام التجاري**
- ✅ **الاستخدام الشخصي**
- ✅ **التعديل والتطوير**
- ✅ **إعادة التوزيع**

## 📞 الدعم والتواصل

للدعم والاستفسارات:
- 🐛 **فتح Issue** في GitHub للأخطاء
- 💡 **طلب ميزة جديدة** عبر Issues
- 📧 **التواصل المباشر** للاستشارات

## 🎉 شكر خاص

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا النظام.

---

**تم تطوير هذا النظام بعناية فائقة ليكون الحل الأمثل لنقاط البيع في العالم العربي** 🇸🇦
