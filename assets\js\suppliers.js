/**
 * إدارة الموردين - JavaScript
 */

let allSuppliers = [];
let filteredSuppliers = [];

// تحميل الموردين عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash === '#suppliers' || document.getElementById('suppliers-page').style.display !== 'none') {
        loadSuppliersPage();
    }
});

// تحميل صفحة الموردين
async function loadSuppliersPage() {
    await loadAllSuppliers();
    updateSuppliersStats();
    displaySuppliers(allSuppliers);
}

// تحميل جميع الموردين
async function loadAllSuppliers() {
    try {
        const response = await fetch('api/suppliers.php/list');
        const result = await response.json();
        
        if (result.success) {
            allSuppliers = result.data;
            filteredSuppliers = [...allSuppliers];
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل الموردين:', error);
        showAlert('خطأ في تحميل بيانات الموردين', 'danger');
        // بيانات تجريبية في حالة الخطأ
        allSuppliers = [
            { id: 1, name: 'شركة التوريدات المتقدمة', contact_person: 'أحمد محمد', phone: '0501234567', email: '<EMAIL>', balance: 0, is_active: 1 },
            { id: 2, name: 'مؤسسة الجودة للتجارة', contact_person: 'فاطمة علي', phone: '0507654321', email: '<EMAIL>', balance: -1500.00, is_active: 1 },
            { id: 3, name: 'شركة الإمداد السريع', contact_person: 'محمد سعد', phone: '0509876543', email: '<EMAIL>', balance: 500.00, is_active: 1 }
        ];
        filteredSuppliers = [...allSuppliers];
    }
}

// تحديث إحصائيات الموردين
function updateSuppliersStats() {
    const totalSuppliers = allSuppliers.length;
    const activeSuppliers = allSuppliers.filter(s => s.is_active).length;
    const totalBalance = allSuppliers.reduce((sum, s) => sum + (s.balance || 0), 0);

    document.getElementById('total-suppliers').textContent = totalSuppliers;
    document.getElementById('active-suppliers').textContent = activeSuppliers;
    document.getElementById('total-supplier-balance').textContent = totalBalance.toFixed(2);
}

// عرض الموردين في الجدول
function displaySuppliers(suppliers) {
    const tbody = document.getElementById('suppliers-table');
    tbody.innerHTML = '';

    if (suppliers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد بيانات</td></tr>';
        return;
    }

    suppliers.forEach(supplier => {
        const row = document.createElement('tr');
        const balanceClass = supplier.balance > 0 ? 'text-success' : supplier.balance < 0 ? 'text-danger' : 'text-muted';
        const statusBadge = supplier.is_active ? 
            '<span class="badge bg-success">نشط</span>' : 
            '<span class="badge bg-secondary">غير نشط</span>';

        row.innerHTML = `
            <td><strong>${supplier.name}</strong></td>
            <td>${supplier.contact_person || '-'}</td>
            <td>${supplier.phone || '-'}</td>
            <td>${supplier.email || '-'}</td>
            <td class="${balanceClass}">
                <strong>${(supplier.balance || 0).toFixed(2)} ر.س</strong>
            </td>
            <td>${statusBadge}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewSupplierDetails(${supplier.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="editSupplier(${supplier.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteSupplier(${supplier.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// البحث في الموردين
function searchSuppliers() {
    const searchTerm = document.getElementById('supplier-search').value.toLowerCase();
    
    if (searchTerm === '') {
        filteredSuppliers = [...allSuppliers];
    } else {
        filteredSuppliers = allSuppliers.filter(supplier => 
            supplier.name.toLowerCase().includes(searchTerm) ||
            (supplier.contact_person && supplier.contact_person.toLowerCase().includes(searchTerm)) ||
            (supplier.phone && supplier.phone.includes(searchTerm)) ||
            (supplier.email && supplier.email.toLowerCase().includes(searchTerm))
        );
    }
    
    displaySuppliers(filteredSuppliers);
}

// تحديث الموردين
function refreshSuppliers() {
    loadSuppliersPage();
    showAlert('تم تحديث بيانات الموردين', 'success');
}

// إظهار مودال إضافة مورد
function showAddSupplierModal() {
    document.getElementById('supplierModalTitle').textContent = 'إضافة مورد جديد';
    document.getElementById('supplierForm').reset();
    document.getElementById('supplierId').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
    modal.show();
}

// تعديل مورد
function editSupplier(supplierId) {
    const supplier = allSuppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    document.getElementById('supplierModalTitle').textContent = 'تعديل بيانات المورد';
    document.getElementById('supplierId').value = supplier.id;
    document.getElementById('supplierName').value = supplier.name;
    document.getElementById('supplierContactPerson').value = supplier.contact_person || '';
    document.getElementById('supplierPhone').value = supplier.phone || '';
    document.getElementById('supplierEmail').value = supplier.email || '';
    document.getElementById('supplierAddress').value = supplier.address || '';

    const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
    modal.show();
}

// حفظ المورد
async function saveSupplier() {
    const supplierId = document.getElementById('supplierId').value;
    const supplierData = {
        name: document.getElementById('supplierName').value.trim(),
        contact_person: document.getElementById('supplierContactPerson').value.trim(),
        phone: document.getElementById('supplierPhone').value.trim(),
        email: document.getElementById('supplierEmail').value.trim(),
        address: document.getElementById('supplierAddress').value.trim()
    };

    if (!supplierData.name) {
        showAlert('يرجى إدخال اسم المورد', 'warning');
        return;
    }

    try {
        const url = supplierId ? `api/suppliers.php/${supplierId}` : 'api/suppliers.php';
        const method = supplierId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(supplierData)
        });

        const result = await response.json();

        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('supplierModal')).hide();
            showAlert(supplierId ? 'تم تحديث بيانات المورد بنجاح' : 'تم إضافة المورد بنجاح', 'success');
            loadSuppliersPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ المورد:', error);
        showAlert('خطأ في حفظ بيانات المورد', 'danger');
    }
}

// حذف مورد
async function deleteSupplier(supplierId) {
    const supplier = allSuppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    if (!confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟`)) {
        return;
    }

    try {
        const response = await fetch(`api/suppliers.php/${supplierId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم حذف المورد بنجاح', 'success');
            loadSuppliersPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف المورد:', error);
        showAlert('خطأ في حذف المورد', 'danger');
    }
}

// عرض تفاصيل المورد
async function viewSupplierDetails(supplierId) {
    try {
        const response = await fetch(`api/suppliers.php/${supplierId}`);
        const result = await response.json();

        if (result.success) {
            const supplier = result.data;

            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات المورد:</h6>
                        <p><strong>اسم المورد:</strong> ${supplier.name}</p>
                        <p><strong>الشخص المسؤول:</strong> ${supplier.contact_person || '-'}</p>
                        <p><strong>رقم الجوال:</strong> ${supplier.phone || '-'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${supplier.email || '-'}</p>
                        <p><strong>العنوان:</strong> ${supplier.address || '-'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات إضافية:</h6>
                        <p><strong>الرصيد:</strong> ${(supplier.balance || 0).toFixed(2)} ر.س</p>
                        <p><strong>تاريخ التسجيل:</strong> ${new Date(supplier.created_at).toLocaleDateString('ar-SA')}</p>
                        <p><strong>الحالة:</strong> ${supplier.is_active ? 'نشط' : 'غير نشط'}</p>
                    </div>
                </div>
            `;

            // إنشاء مودال مؤقت لعرض التفاصيل
            const modalHtml = `
                <div class="modal fade" id="supplierDetailsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تفاصيل المورد</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة المودال السابق إن وجد
            const existingModal = document.getElementById('supplierDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة المودال الجديد
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            const modal = new bootstrap.Modal(document.getElementById('supplierDetailsModal'));
            modal.show();

            // إزالة المودال عند الإغلاق
            document.getElementById('supplierDetailsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });

        } else {
            showAlert('خطأ في تحميل تفاصيل المورد', 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل تفاصيل المورد:', error);
        showAlert('خطأ في تحميل تفاصيل المورد', 'danger');
    }
}
