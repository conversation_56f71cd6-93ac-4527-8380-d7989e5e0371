<?php
/**
 * API للتعامل مع الفواتير المعلقة (Hold Invoices)
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

class HeldInvoicesAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    /**
     * معالجة الطلبات
     */
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    if ($path === '/list') {
                        $this->getHeldInvoices();
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'POST':
                    if ($path === '/hold') {
                        $this->holdInvoice();
                    } elseif ($path === '/resume') {
                        $this->resumeInvoice();
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'DELETE':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->deleteHeldInvoice($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Server error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * الحصول على قائمة الفواتير المعلقة
     */
    private function getHeldInvoices() {
        $userId = $_GET['user_id'] ?? 1; // افتراضياً المستخدم الأول

        $sql = "SELECT hi.*, c.name as customer_name, u.full_name as user_name 
                FROM held_invoices hi 
                LEFT JOIN customers c ON hi.customer_id = c.id 
                LEFT JOIN users u ON hi.user_id = u.id 
                WHERE hi.user_id = ? 
                ORDER BY hi.held_at DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$userId]);
        $heldInvoices = $stmt->fetchAll();

        // تحويل بيانات JSON إلى مصفوفة
        foreach ($heldInvoices as &$invoice) {
            $invoice['invoice_data'] = json_decode($invoice['invoice_data'], true);
        }

        $this->sendSuccess($heldInvoices);
    }

    /**
     * تعليق فاتورة جديدة
     */
    private function holdInvoice() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['invoice_data'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $invoiceData = $input['invoice_data'];
        $userId = $input['user_id'] ?? 1;
        $customerId = $input['customer_id'] ?? null;

        // التحقق من صحة البيانات
        if (empty($invoiceData['items']) || !is_array($invoiceData['items'])) {
            $this->sendError('Invoice must have items', 400);
            return;
        }

        // حساب الإجمالي وعدد العناصر
        $totalAmount = 0;
        $itemsCount = count($invoiceData['items']);

        foreach ($invoiceData['items'] as $item) {
            $totalAmount += ($item['price'] * $item['quantity']);
        }

        // تطبيق الخصم
        if (isset($invoiceData['discount']) && $invoiceData['discount'] > 0) {
            $discountAmount = ($totalAmount * $invoiceData['discount']) / 100;
            $totalAmount -= $discountAmount;
        }

        try {
            $sql = "INSERT INTO held_invoices (invoice_data, user_id, customer_id, total_amount, items_count) 
                    VALUES (?, ?, ?, ?, ?)";

            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                json_encode($invoiceData, JSON_UNESCAPED_UNICODE),
                $userId,
                $customerId,
                $totalAmount,
                $itemsCount
            ]);

            if ($result) {
                $heldInvoiceId = $this->conn->lastInsertId();
                $this->sendSuccess([
                    'id' => $heldInvoiceId,
                    'message' => 'تم تعليق الفاتورة بنجاح'
                ]);
            } else {
                $this->sendError('Failed to hold invoice', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * استكمال فاتورة معلقة
     */
    private function resumeInvoice() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['held_invoice_id'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $heldInvoiceId = $input['held_invoice_id'];
        $userId = $input['user_id'] ?? 1;

        try {
            // الحصول على بيانات الفاتورة المعلقة
            $sql = "SELECT * FROM held_invoices WHERE id = ? AND user_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$heldInvoiceId, $userId]);
            $heldInvoice = $stmt->fetch();

            if (!$heldInvoice) {
                $this->sendError('Held invoice not found', 404);
                return;
            }

            // حذف الفاتورة المعلقة
            $sql = "DELETE FROM held_invoices WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$heldInvoiceId]);

            if ($result) {
                $invoiceData = json_decode($heldInvoice['invoice_data'], true);
                $this->sendSuccess([
                    'invoice_data' => $invoiceData,
                    'message' => 'تم استكمال الفاتورة المعلقة'
                ]);
            } else {
                $this->sendError('Failed to resume invoice', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * حذف فاتورة معلقة
     */
    private function deleteHeldInvoice($heldInvoiceId) {
        $userId = $_GET['user_id'] ?? 1;

        try {
            $sql = "DELETE FROM held_invoices WHERE id = ? AND user_id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$heldInvoiceId, $userId]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف الفاتورة المعلقة']);
            } else {
                $this->sendError('Held invoice not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * إرسال استجابة ناجحة
     */
    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
    }

    /**
     * إرسال رسالة خطأ
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
    }
}

// تشغيل API
$api = new HeldInvoicesAPI();
$api->handleRequest();
?>
