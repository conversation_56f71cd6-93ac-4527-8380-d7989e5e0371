<?php
/**
 * API للتعامل مع العملاء
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

class CustomersAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    if ($path === '/list') {
                        $this->getCustomers();
                    } elseif ($path === '/search') {
                        $this->searchCustomers();
                    } elseif (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->getCustomer($matches[1]);
                    } elseif (preg_match('/\/(\d+)\/balance/', $path, $matches)) {
                        $this->getCustomerBalance($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'POST':
                    if ($path === '/payment') {
                        $this->addPayment();
                    } else {
                        $this->createCustomer();
                    }
                    break;

                case 'PUT':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->updateCustomer($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'DELETE':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->deleteCustomer($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Server error: ' . $e->getMessage(), 500);
        }
    }

    private function getCustomers() {
        $sql = "SELECT * FROM customers WHERE is_active = 1 ORDER BY name";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $customers = $stmt->fetchAll();

        $this->sendSuccess($customers);
    }

    private function searchCustomers() {
        $search = $_GET['q'] ?? '';
        
        if (empty($search)) {
            $this->getCustomers();
            return;
        }

        $sql = "SELECT * FROM customers 
                WHERE is_active = 1 
                AND (name LIKE ? OR phone LIKE ? OR email LIKE ?) 
                ORDER BY name";

        $searchTerm = '%' . $search . '%';
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        $customers = $stmt->fetchAll();

        $this->sendSuccess($customers);
    }

    private function getCustomer($id) {
        $sql = "SELECT * FROM customers WHERE id = ? AND is_active = 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $customer = $stmt->fetch();

        if ($customer) {
            // إضافة تاريخ آخر عملية شراء
            $sql = "SELECT MAX(created_at) as last_purchase FROM invoices WHERE customer_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            $lastPurchase = $stmt->fetch();
            
            $customer['last_purchase'] = $lastPurchase['last_purchase'];
            
            $this->sendSuccess($customer);
        } else {
            $this->sendError('Customer not found', 404);
        }
    }

    private function getCustomerBalance($id) {
        // حساب الرصيد من الفواتير والمدفوعات
        $sql = "SELECT 
                    COALESCE(SUM(CASE WHEN payment_method = 'credit' THEN total_amount ELSE 0 END), 0) as total_credit,
                    COALESCE(SUM(paid_amount), 0) as total_paid
                FROM invoices 
                WHERE customer_id = ? AND status = 'completed'";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $invoiceData = $stmt->fetch();

        // المدفوعات الإضافية
        $sql = "SELECT COALESCE(SUM(amount), 0) as additional_payments 
                FROM payments 
                WHERE customer_id = ? AND invoice_id IS NULL";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $paymentData = $stmt->fetch();

        $balance = $invoiceData['total_credit'] - $invoiceData['total_paid'] - $paymentData['additional_payments'];

        $this->sendSuccess([
            'customer_id' => $id,
            'balance' => $balance,
            'total_credit' => $invoiceData['total_credit'],
            'total_paid' => $invoiceData['total_paid'] + $paymentData['additional_payments']
        ]);
    }

    private function createCustomer() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO customers (name, phone, email, address, credit_limit) 
                VALUES (?, ?, ?, ?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['phone'] ?? null,
                $input['email'] ?? null,
                $input['address'] ?? null,
                $input['credit_limit'] ?? 0
            ]);

            if ($result) {
                $customerId = $this->conn->lastInsertId();
                $this->sendSuccess(['id' => $customerId, 'message' => 'تم إضافة العميل بنجاح']);
            } else {
                $this->sendError('Failed to create customer', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function updateCustomer($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "UPDATE customers SET 
                name = ?, phone = ?, email = ?, address = ?, 
                credit_limit = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND is_active = 1";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'] ?? '',
                $input['phone'] ?? null,
                $input['email'] ?? null,
                $input['address'] ?? null,
                $input['credit_limit'] ?? 0,
                $id
            ]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم تحديث العميل بنجاح']);
            } else {
                $this->sendError('Customer not found or no changes made', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function addPayment() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['customer_id']) || !isset($input['amount'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO payments (customer_id, amount, payment_method, reference_number, notes, user_id) 
                VALUES (?, ?, ?, ?, ?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['customer_id'],
                $input['amount'],
                $input['payment_method'] ?? 'cash',
                $input['reference_number'] ?? null,
                $input['notes'] ?? null,
                $input['user_id'] ?? 1
            ]);

            if ($result) {
                $paymentId = $this->conn->lastInsertId();
                
                // تحديث رصيد العميل
                $this->updateCustomerBalance($input['customer_id']);
                
                $this->sendSuccess(['id' => $paymentId, 'message' => 'تم تسجيل الدفعة بنجاح']);
            } else {
                $this->sendError('Failed to add payment', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function updateCustomerBalance($customerId) {
        // حساب الرصيد الجديد
        $sql = "SELECT 
                    COALESCE(SUM(CASE WHEN payment_method = 'credit' THEN total_amount ELSE 0 END), 0) as total_credit,
                    COALESCE(SUM(paid_amount), 0) as total_paid
                FROM invoices 
                WHERE customer_id = ? AND status = 'completed'";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customerId]);
        $invoiceData = $stmt->fetch();

        $sql = "SELECT COALESCE(SUM(amount), 0) as additional_payments 
                FROM payments 
                WHERE customer_id = ? AND invoice_id IS NULL";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$customerId]);
        $paymentData = $stmt->fetch();

        $balance = $invoiceData['total_credit'] - $invoiceData['total_paid'] - $paymentData['additional_payments'];

        // تحديث رصيد العميل
        $sql = "UPDATE customers SET balance = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$balance, $customerId]);
    }

    private function deleteCustomer($id) {
        $sql = "UPDATE customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف العميل بنجاح']);
            } else {
                $this->sendError('Customer not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
    }
}

$api = new CustomersAPI();
$api->handleRequest();
?>
