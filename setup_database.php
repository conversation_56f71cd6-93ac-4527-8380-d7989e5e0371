<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h3><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h3>
                        <p class="mb-0">نظام نقاط البيع</p>
                    </div>
                    <div class="card-body">
                        <?php
                        require_once 'config/database.php';

                        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                            echo '<div class="alert alert-info">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    جاري إعداد قاعدة البيانات...
                                  </div>';

                            $db = new Database();
                            
                            echo '<div class="mt-3">';
                            echo '<h5>خطوات الإعداد:</h5>';
                            echo '<hr>';
                            
                            // إنشاء قاعدة البيانات والجداول
                            $db->createDatabase();
                            
                            echo '<hr>';
                            echo '<div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم إعداد قاعدة البيانات بنجاح!
                                  </div>';
                            
                            echo '<div class="text-center mt-4">
                                    <a href="index.html" class="btn btn-primary btn-lg">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        الانتقال إلى النظام
                                    </a>
                                  </div>';
                            echo '</div>';
                        } else {
                            ?>
                            <div class="text-center mb-4">
                                <i class="fas fa-database text-primary" style="font-size: 4rem;"></i>
                                <h4 class="mt-3">مرحباً بك في نظام نقاط البيع</h4>
                                <p class="text-muted">يرجى إعداد قاعدة البيانات قبل البدء في استخدام النظام</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>متطلبات النظام:</h6>
                                <ul class="mb-0">
                                    <li>خادم MySQL يعمل (XAMPP, WAMP, أو MAMP)</li>
                                    <li>PHP 7.4 أو أحدث</li>
                                    <li>امتداد PDO MySQL مفعل</li>
                                </ul>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>إعدادات قاعدة البيانات الافتراضية:</h6>
                                <ul class="mb-0">
                                    <li><strong>الخادم:</strong> localhost</li>
                                    <li><strong>اسم قاعدة البيانات:</strong> pos_system</li>
                                    <li><strong>اسم المستخدم:</strong> root</li>
                                    <li><strong>كلمة المرور:</strong> (فارغة)</li>
                                </ul>
                                <small class="text-muted">يمكنك تغيير هذه الإعدادات في ملف config/database.php</small>
                            </div>

                            <?php
                            // اختبار الاتصال أولاً
                            echo '<div class="mb-4">';
                            echo '<h6>اختبار الاتصال بـ MySQL:</h6>';
                            
                            try {
                                $testConnection = new PDO("mysql:host=localhost", "root", "");
                                echo '<div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        تم الاتصال بخادم MySQL بنجاح
                                      </div>';
                                $canProceed = true;
                            } catch(PDOException $e) {
                                echo '<div class="alert alert-danger">
                                        <i class="fas fa-times-circle me-2"></i>
                                        فشل الاتصال بخادم MySQL: ' . $e->getMessage() . '
                                      </div>';
                                echo '<div class="alert alert-warning">
                                        <strong>حلول مقترحة:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>تأكد من تشغيل XAMPP أو WAMP</li>
                                            <li>تأكد من تشغيل خدمة MySQL</li>
                                            <li>تحقق من إعدادات الاتصال في config/database.php</li>
                                        </ul>
                                      </div>';
                                $canProceed = false;
                            }
                            echo '</div>';

                            if ($canProceed) {
                                ?>
                                <form method="POST" class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-play me-2"></i>
                                        بدء إعداد قاعدة البيانات
                                    </button>
                                </form>

                                <div class="mt-4">
                                    <h6>ما سيتم إنشاؤه:</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول المستخدمين</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول العملاء</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول الموردين</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول المنتجات</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول الفئات</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول الفواتير</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول عناصر الفواتير</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول الفواتير المعلقة</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول المدفوعات</li>
                                                <li class="list-group-item"><i class="fas fa-table text-primary me-2"></i>جدول حركات المخزون</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-success mt-4">
                                    <h6><i class="fas fa-user-shield me-2"></i>بيانات تسجيل الدخول الافتراضية:</h6>
                                    <p class="mb-0">
                                        <strong>اسم المستخدم:</strong> admin<br>
                                        <strong>كلمة المرور:</strong> admin123
                                    </p>
                                    <small class="text-muted">يرجى تغيير كلمة المرور بعد تسجيل الدخول</small>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <p class="text-muted">
                        <i class="fas fa-code me-1"></i>
                        نظام نقاط البيع - الإصدار 1.0
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
