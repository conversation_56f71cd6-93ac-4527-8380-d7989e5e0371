<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفواتير المعلقة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="test-card">
                    <h2 class="text-center mb-4">
                        <i class="fas fa-clipboard-check text-primary me-2"></i>
                        اختبار وظائف الفواتير المعلقة
                    </h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-list-check text-success me-2"></i>الاختبارات المطلوبة:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">✅ تعليق فاتورة جديدة</li>
                                <li class="list-group-item">✅ عرض الفواتير المعلقة</li>
                                <li class="list-group-item">✅ استكمال فاتورة معلقة</li>
                                <li class="list-group-item">✅ حذف فاتورة معلقة</li>
                                <li class="list-group-item">✅ تحديث العداد</li>
                                <li class="list-group-item">✅ الحفظ في قاعدة البيانات</li>
                                <li class="list-group-item">✅ النسخ الاحتياطي المحلي</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="fas fa-keyboard text-info me-2"></i>اختصارات لوحة المفاتيح:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><kbd>F1</kbd> فاتورة جديدة</li>
                                <li class="list-group-item"><kbd>F2</kbd> تعليق الفاتورة</li>
                                <li class="list-group-item"><kbd>F3</kbd> عرض الفواتير المعلقة</li>
                                <li class="list-group-item"><kbd>F4</kbd> إتمام البيع</li>
                                <li class="list-group-item"><kbd>F5</kbd> طباعة الفاتورة</li>
                                <li class="list-group-item"><kbd>F9</kbd> آلة حاسبة</li>
                                <li class="list-group-item"><kbd>Escape</kbd> إغلاق النوافذ</li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <h5><i class="fas fa-play-circle text-primary me-2"></i>خطوات الاختبار:</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="test-info">
                                    <h6>1. إنشاء فاتورة</h6>
                                    <p>أضف منتجات للفاتورة واختر عميل</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="test-info">
                                    <h6>2. تعليق الفاتورة</h6>
                                    <p>اضغط F2 أو زر "تعليق الفاتورة"</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="test-info">
                                    <h6>3. إدارة الفواتير</h6>
                                    <p>اضغط F3 لعرض واستكمال أو حذف</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <a href="index.html" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            بدء الاختبار
                        </a>
                        <a href="setup_database.php" class="btn btn-secondary btn-lg ms-2">
                            <i class="fas fa-database me-2"></i>
                            إعداد قاعدة البيانات
                        </a>
                    </div>
                </div>
                
                <div class="test-card">
                    <h5><i class="fas fa-bug text-warning me-2"></i>نصائح لحل المشاكل:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>مشاكل قاعدة البيانات:</h6>
                            <ul>
                                <li>تأكد من تشغيل XAMPP</li>
                                <li>تأكد من إعداد قاعدة البيانات</li>
                                <li>تحقق من اتصال الإنترنت</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>مشاكل الواجهة:</h6>
                            <ul>
                                <li>تحديث الصفحة (F5)</li>
                                <li>مسح ذاكرة التخزين المؤقت</li>
                                <li>تحقق من وحدة التحكم (F12)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
