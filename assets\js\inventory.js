/**
 * إدارة المخزون - JavaScript
 */

let allProducts = [];
let filteredProducts = [];
let categories = [];
let suppliers = [];

// تحميل المخزون عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash === '#inventory' || document.getElementById('inventory-page').style.display !== 'none') {
        loadInventoryPage();
    }
});

// تحميل صفحة المخزون
async function loadInventoryPage() {
    await Promise.all([
        loadAllProducts(),
        loadCategories(),
        loadSuppliersForDropdown()
    ]);
    updateInventoryStats();
    displayInventory(allProducts);
    populateDropdowns();
}

// تحميل جميع المنتجات
async function loadAllProducts() {
    try {
        const response = await fetch('api/products.php/list');
        const result = await response.json();
        
        if (result.success) {
            allProducts = result.data;
            filteredProducts = [...allProducts];
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        showAlert('خطأ في تحميل بيانات المخزون', 'danger');
        // بيانات تجريبية في حالة الخطأ
        allProducts = [
            { id: 1, name: 'منتج تجريبي 1', barcode: '1234567890', cost_price: 20.00, selling_price: 25.50, stock_quantity: 100, min_stock_level: 10, category_name: 'عام', supplier_name: 'مورد 1', unit: 'قطعة', is_active: 1 },
            { id: 2, name: 'منتج تجريبي 2', barcode: '1234567891', cost_price: 12.00, selling_price: 15.75, stock_quantity: 5, min_stock_level: 10, category_name: 'عام', supplier_name: 'مورد 2', unit: 'قطعة', is_active: 1 }
        ];
        filteredProducts = [...allProducts];
    }
}

// تحميل الفئات
async function loadCategories() {
    try {
        const response = await fetch('api/categories.php/list');
        const result = await response.json();
        
        if (result.success) {
            categories = result.data;
        } else {
            categories = [{ id: 1, name: 'عام' }];
        }
    } catch (error) {
        categories = [{ id: 1, name: 'عام' }];
    }
}

// تحميل الموردين للقائمة المنسدلة
async function loadSuppliersForDropdown() {
    try {
        const response = await fetch('api/suppliers.php/list');
        const result = await response.json();
        
        if (result.success) {
            suppliers = result.data;
        } else {
            suppliers = [];
        }
    } catch (error) {
        suppliers = [];
    }
}

// ملء القوائم المنسدلة
function populateDropdowns() {
    // ملء فلتر الفئات
    const categoryFilter = document.getElementById('category-filter');
    categoryFilter.innerHTML = '<option value="all">جميع الفئات</option>';
    categories.forEach(category => {
        categoryFilter.innerHTML += `<option value="${category.id}">${category.name}</option>`;
    });

    // ملء قائمة الفئات في المودال
    const productCategory = document.getElementById('productCategory');
    productCategory.innerHTML = '<option value="">اختر الفئة</option>';
    categories.forEach(category => {
        productCategory.innerHTML += `<option value="${category.id}">${category.name}</option>`;
    });

    // ملء قائمة الموردين في المودال
    const productSupplier = document.getElementById('productSupplier');
    productSupplier.innerHTML = '<option value="">اختر المورد</option>';
    suppliers.forEach(supplier => {
        productSupplier.innerHTML += `<option value="${supplier.id}">${supplier.name}</option>`;
    });
}

// تحديث إحصائيات المخزون
function updateInventoryStats() {
    const totalProducts = allProducts.length;
    const availableProducts = allProducts.filter(p => p.stock_quantity > 0).length;
    const lowStockProducts = allProducts.filter(p => p.stock_quantity <= p.min_stock_level && p.stock_quantity > 0).length;
    const outOfStockProducts = allProducts.filter(p => p.stock_quantity === 0).length;

    document.getElementById('total-products').textContent = totalProducts;
    document.getElementById('available-products').textContent = availableProducts;
    document.getElementById('low-stock-products').textContent = lowStockProducts;
    document.getElementById('out-of-stock-products').textContent = outOfStockProducts;
}

// عرض المخزون في الجدول
function displayInventory(products) {
    const tbody = document.getElementById('inventory-table');
    tbody.innerHTML = '';

    if (products.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">لا توجد بيانات</td></tr>';
        return;
    }

    products.forEach(product => {
        const row = document.createElement('tr');
        
        // تحديد حالة المخزون
        let stockStatus = '';
        let stockClass = '';
        if (product.stock_quantity === 0) {
            stockStatus = 'نفد';
            stockClass = 'text-danger';
        } else if (product.stock_quantity <= product.min_stock_level) {
            stockStatus = 'منخفض';
            stockClass = 'text-warning';
        } else {
            stockStatus = 'متوفر';
            stockClass = 'text-success';
        }

        row.innerHTML = `
            <td>
                <strong>${product.name}</strong>
                ${product.description ? `<br><small class="text-muted">${product.description}</small>` : ''}
            </td>
            <td>${product.barcode || '-'}</td>
            <td>${product.category_name || '-'}</td>
            <td>${parseFloat(product.cost_price).toFixed(2)} ر.س</td>
            <td>${parseFloat(product.selling_price).toFixed(2)} ر.س</td>
            <td>
                <strong class="${stockClass}">${product.stock_quantity} ${product.unit}</strong>
            </td>
            <td>${product.min_stock_level} ${product.unit}</td>
            <td>
                <span class="badge bg-${stockClass.includes('success') ? 'success' : stockClass.includes('warning') ? 'warning' : 'danger'}">
                    ${stockStatus}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-success" onclick="editProduct(${product.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="adjustStock(${product.id})" title="تعديل المخزون">
                        <i class="fas fa-boxes"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteProduct(${product.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// البحث في المخزون
function searchInventory() {
    const searchTerm = document.getElementById('inventory-search').value.toLowerCase();
    
    if (searchTerm === '') {
        filteredProducts = [...allProducts];
    } else {
        filteredProducts = allProducts.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            (product.barcode && product.barcode.includes(searchTerm)) ||
            (product.category_name && product.category_name.toLowerCase().includes(searchTerm))
        );
    }
    
    filterInventory();
}

// فلترة المخزون
function filterInventory() {
    const stockFilter = document.getElementById('inventory-filter').value;
    const categoryFilter = document.getElementById('category-filter').value;
    let productsToShow = [...filteredProducts];

    // فلترة حسب حالة المخزون
    switch (stockFilter) {
        case 'available':
            productsToShow = productsToShow.filter(p => p.stock_quantity > 0);
            break;
        case 'low-stock':
            productsToShow = productsToShow.filter(p => p.stock_quantity <= p.min_stock_level && p.stock_quantity > 0);
            break;
        case 'out-of-stock':
            productsToShow = productsToShow.filter(p => p.stock_quantity === 0);
            break;
    }

    // فلترة حسب الفئة
    if (categoryFilter !== 'all') {
        productsToShow = productsToShow.filter(p => p.category_id == categoryFilter);
    }

    displayInventory(productsToShow);
}

// تحديث المخزون
function refreshInventory() {
    loadInventoryPage();
    showAlert('تم تحديث بيانات المخزون', 'success');
}

// إظهار مودال إضافة منتج
function showAddProductModal() {
    document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productForm').reset();
    document.getElementById('productId').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// تعديل منتج
function editProduct(productId) {
    const product = allProducts.find(p => p.id === productId);
    if (!product) return;

    document.getElementById('productModalTitle').textContent = 'تعديل بيانات المنتج';
    document.getElementById('productId').value = product.id;
    document.getElementById('productName').value = product.name;
    document.getElementById('productBarcode').value = product.barcode || '';
    document.getElementById('productCategory').value = product.category_id || '';
    document.getElementById('productSupplier').value = product.supplier_id || '';
    document.getElementById('productCostPrice').value = product.cost_price;
    document.getElementById('productSellingPrice').value = product.selling_price;
    document.getElementById('productStock').value = product.stock_quantity;
    document.getElementById('productMinStock').value = product.min_stock_level;
    document.getElementById('productUnit').value = product.unit || 'قطعة';
    document.getElementById('productDescription').value = product.description || '';

    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// حفظ المنتج
async function saveProduct() {
    const productId = document.getElementById('productId').value;
    const productData = {
        name: document.getElementById('productName').value.trim(),
        barcode: document.getElementById('productBarcode').value.trim(),
        category_id: document.getElementById('productCategory').value || null,
        supplier_id: document.getElementById('productSupplier').value || null,
        cost_price: parseFloat(document.getElementById('productCostPrice').value) || 0,
        selling_price: parseFloat(document.getElementById('productSellingPrice').value) || 0,
        stock_quantity: parseInt(document.getElementById('productStock').value) || 0,
        min_stock_level: parseInt(document.getElementById('productMinStock').value) || 0,
        unit: document.getElementById('productUnit').value,
        description: document.getElementById('productDescription').value.trim()
    };

    if (!productData.name || !productData.selling_price) {
        showAlert('يرجى إدخال اسم المنتج وسعر البيع', 'warning');
        return;
    }

    try {
        const url = productId ? `api/products.php/${productId}` : 'api/products.php';
        const method = productId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(productData)
        });

        const result = await response.json();

        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
            showAlert(productId ? 'تم تحديث بيانات المنتج بنجاح' : 'تم إضافة المنتج بنجاح', 'success');
            loadInventoryPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ المنتج:', error);
        showAlert('خطأ في حفظ بيانات المنتج', 'danger');
    }
}

// حذف منتج
async function deleteProduct(productId) {
    const product = allProducts.find(p => p.id === productId);
    if (!product) return;

    if (!confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
        return;
    }

    try {
        const response = await fetch(`api/products.php/${productId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم حذف المنتج بنجاح', 'success');
            loadInventoryPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف المنتج:', error);
        showAlert('خطأ في حذف المنتج', 'danger');
    }
}

// تعديل المخزون
function adjustStock(productId) {
    const product = allProducts.find(p => p.id === productId);
    if (!product) return;

    const newQuantity = prompt(`الكمية الحالية: ${product.stock_quantity} ${product.unit}\nأدخل الكمية الجديدة:`, product.stock_quantity);
    
    if (newQuantity === null || newQuantity === '') return;
    
    const quantity = parseInt(newQuantity);
    if (isNaN(quantity) || quantity < 0) {
        showAlert('يرجى إدخال كمية صحيحة', 'warning');
        return;
    }

    // هنا يمكن إضافة API لتحديث المخزون
    showAlert('سيتم تطبيق هذه الميزة قريباً', 'info');
}

// عرض تنبيهات المخزون المنخفض
function showLowStockAlert() {
    const lowStockProducts = allProducts.filter(p => p.stock_quantity <= p.min_stock_level);
    
    if (lowStockProducts.length === 0) {
        showAlert('لا توجد منتجات بمخزون منخفض', 'success');
        return;
    }

    let alertMessage = 'المنتجات التالية لديها مخزون منخفض:\n\n';
    lowStockProducts.forEach(product => {
        alertMessage += `• ${product.name}: ${product.stock_quantity} ${product.unit} (الحد الأدنى: ${product.min_stock_level})\n`;
    });

    alert(alertMessage);
}
