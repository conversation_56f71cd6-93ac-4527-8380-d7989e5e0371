<?php
/**
 * API للتعامل مع المنتجات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

class ProductsAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    if ($path === '/list') {
                        $this->getProducts();
                    } elseif ($path === '/search') {
                        $this->searchProducts();
                    } elseif (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->getProduct($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'POST':
                    $this->createProduct();
                    break;

                case 'PUT':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->updateProduct($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'DELETE':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->deleteProduct($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Server error: ' . $e->getMessage(), 500);
        }
    }

    private function getProducts() {
        $sql = "SELECT p.*, c.name as category_name, s.name as supplier_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.is_active = 1
                ORDER BY p.name";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $products = $stmt->fetchAll();

        $this->sendSuccess($products);
    }

    private function createProduct() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name']) || !isset($input['selling_price'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO products (name, barcode, category_id, supplier_id, cost_price, selling_price, stock_quantity, min_stock_level, unit, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['barcode'] ?? null,
                $input['category_id'] ?? null,
                $input['supplier_id'] ?? null,
                $input['cost_price'] ?? 0,
                $input['selling_price'],
                $input['stock_quantity'] ?? 0,
                $input['min_stock_level'] ?? 0,
                $input['unit'] ?? 'قطعة',
                $input['description'] ?? null
            ]);

            if ($result) {
                $productId = $this->conn->lastInsertId();
                $this->sendSuccess(['id' => $productId, 'message' => 'تم إضافة المنتج بنجاح']);
            } else {
                $this->sendError('Failed to create product', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function updateProduct($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "UPDATE products SET
                name = ?, barcode = ?, category_id = ?, supplier_id = ?,
                cost_price = ?, selling_price = ?, stock_quantity = ?,
                min_stock_level = ?, unit = ?, description = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND is_active = 1";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'] ?? '',
                $input['barcode'] ?? null,
                $input['category_id'] ?? null,
                $input['supplier_id'] ?? null,
                $input['cost_price'] ?? 0,
                $input['selling_price'] ?? 0,
                $input['stock_quantity'] ?? 0,
                $input['min_stock_level'] ?? 0,
                $input['unit'] ?? 'قطعة',
                $input['description'] ?? null,
                $id
            ]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم تحديث المنتج بنجاح']);
            } else {
                $this->sendError('Product not found or no changes made', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function deleteProduct($id) {
        $sql = "UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف المنتج بنجاح']);
            } else {
                $this->sendError('Product not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function searchProducts() {
        $search = $_GET['q'] ?? '';
        
        if (empty($search)) {
            $this->getProducts();
            return;
        }

        $sql = "SELECT p.*, c.name as category_name, s.name as supplier_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN suppliers s ON p.supplier_id = s.id 
                WHERE p.is_active = 1 
                AND (p.name LIKE ? OR p.barcode LIKE ?) 
                ORDER BY p.name";

        $searchTerm = '%' . $search . '%';
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm]);
        $products = $stmt->fetchAll();

        $this->sendSuccess($products);
    }

    private function getProduct($id) {
        $sql = "SELECT p.*, c.name as category_name, s.name as supplier_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN suppliers s ON p.supplier_id = s.id 
                WHERE p.id = ? AND p.is_active = 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $product = $stmt->fetch();

        if ($product) {
            $this->sendSuccess($product);
        } else {
            $this->sendError('Product not found', 404);
        }
    }

    private function createProduct() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name']) || !isset($input['selling_price'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO products (name, barcode, category_id, supplier_id, cost_price, selling_price, stock_quantity, min_stock_level, unit, description) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['barcode'] ?? null,
                $input['category_id'] ?? null,
                $input['supplier_id'] ?? null,
                $input['cost_price'] ?? 0,
                $input['selling_price'],
                $input['stock_quantity'] ?? 0,
                $input['min_stock_level'] ?? 0,
                $input['unit'] ?? 'قطعة',
                $input['description'] ?? null
            ]);

            if ($result) {
                $productId = $this->conn->lastInsertId();
                $this->sendSuccess(['id' => $productId, 'message' => 'تم إضافة المنتج بنجاح']);
            } else {
                $this->sendError('Failed to create product', 500);
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                $this->sendError('الباركود موجود مسبقاً', 400);
            } else {
                $this->sendError('Database error: ' . $e->getMessage(), 500);
            }
        }
    }

    private function updateProduct($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "UPDATE products SET 
                name = ?, barcode = ?, category_id = ?, supplier_id = ?, 
                cost_price = ?, selling_price = ?, stock_quantity = ?, 
                min_stock_level = ?, unit = ?, description = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND is_active = 1";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'] ?? '',
                $input['barcode'] ?? null,
                $input['category_id'] ?? null,
                $input['supplier_id'] ?? null,
                $input['cost_price'] ?? 0,
                $input['selling_price'] ?? 0,
                $input['stock_quantity'] ?? 0,
                $input['min_stock_level'] ?? 0,
                $input['unit'] ?? 'قطعة',
                $input['description'] ?? null,
                $id
            ]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم تحديث المنتج بنجاح']);
            } else {
                $this->sendError('Product not found or no changes made', 404);
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                $this->sendError('الباركود موجود مسبقاً', 400);
            } else {
                $this->sendError('Database error: ' . $e->getMessage(), 500);
            }
        }
    }

    private function deleteProduct($id) {
        $sql = "UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف المنتج بنجاح']);
            } else {
                $this->sendError('Product not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
    }
}

$api = new ProductsAPI();
$api->handleRequest();
?>
