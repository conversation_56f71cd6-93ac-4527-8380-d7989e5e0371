/**
 * إدارة العملاء - JavaScript
 */

let allCustomers = [];
let filteredCustomers = [];
let currentCustomer = null;

// تحميل العملاء عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash === '#customers' || document.getElementById('customers-page').style.display !== 'none') {
        loadCustomersPage();
    }
});

// تحميل صفحة العملاء
async function loadCustomersPage() {
    await loadAllCustomers();
    updateCustomersStats();
    displayCustomers(allCustomers);
}

// تحميل جميع العملاء
async function loadAllCustomers() {
    try {
        const response = await fetch('api/customers.php/list');
        const result = await response.json();
        
        if (result.success) {
            allCustomers = result.data.filter(customer => customer.id !== 'cash');
            filteredCustomers = [...allCustomers];
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
        showAlert('خطأ في تحميل بيانات العملاء', 'danger');
        // بيانات تجريبية في حالة الخطأ
        allCustomers = [
            { id: 1, name: 'أحمد محمد', phone: '0501234567', email: '<EMAIL>', balance: 150.00, is_active: 1 },
            { id: 2, name: 'فاطمة علي', phone: '0507654321', email: '<EMAIL>', balance: -75.50, is_active: 1 },
            { id: 3, name: 'محمد سعد', phone: '0509876543', email: '<EMAIL>', balance: 0, is_active: 1 }
        ];
        filteredCustomers = [...allCustomers];
    }
}

// تحديث إحصائيات العملاء
function updateCustomersStats() {
    const totalCustomers = allCustomers.length;
    const activeCustomers = allCustomers.filter(c => c.is_active).length;
    const totalDebts = allCustomers.reduce((sum, c) => sum + Math.max(0, -c.balance), 0);
    const avgBalance = totalCustomers > 0 ? allCustomers.reduce((sum, c) => sum + c.balance, 0) / totalCustomers : 0;

    document.getElementById('total-customers').textContent = totalCustomers;
    document.getElementById('active-customers').textContent = activeCustomers;
    document.getElementById('total-debts').textContent = totalDebts.toFixed(2);
    document.getElementById('avg-balance').textContent = avgBalance.toFixed(2);
}

// عرض العملاء في الجدول
function displayCustomers(customers) {
    const tbody = document.getElementById('customers-table');
    tbody.innerHTML = '';

    if (customers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد بيانات</td></tr>';
        return;
    }

    customers.forEach(customer => {
        const row = document.createElement('tr');
        const balanceClass = customer.balance > 0 ? 'text-success' : customer.balance < 0 ? 'text-danger' : 'text-muted';
        const statusBadge = customer.is_active ? 
            '<span class="badge bg-success">نشط</span>' : 
            '<span class="badge bg-secondary">غير نشط</span>';

        row.innerHTML = `
            <td>
                <strong>${customer.name}</strong>
                ${customer.credit_limit > 0 ? `<br><small class="text-muted">حد الائتمان: ${customer.credit_limit} ر.س</small>` : ''}
            </td>
            <td>${customer.phone || '-'}</td>
            <td>${customer.email || '-'}</td>
            <td class="${balanceClass}">
                <strong>${customer.balance.toFixed(2)} ر.س</strong>
            </td>
            <td>${customer.last_purchase ? new Date(customer.last_purchase).toLocaleDateString('ar-SA') : '-'}</td>
            <td>${statusBadge}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewCustomerDetails(${customer.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="editCustomer(${customer.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="showAddPaymentModal(${customer.id})" title="إضافة دفعة">
                        <i class="fas fa-money-bill"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCustomer(${customer.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// البحث في العملاء
function searchCustomers() {
    const searchTerm = document.getElementById('customer-search').value.toLowerCase();
    
    if (searchTerm === '') {
        filteredCustomers = [...allCustomers];
    } else {
        filteredCustomers = allCustomers.filter(customer => 
            customer.name.toLowerCase().includes(searchTerm) ||
            (customer.phone && customer.phone.includes(searchTerm)) ||
            (customer.email && customer.email.toLowerCase().includes(searchTerm))
        );
    }
    
    filterCustomers();
}

// فلترة العملاء
function filterCustomers() {
    const filter = document.getElementById('customer-filter').value;
    let customersToShow = [...filteredCustomers];

    switch (filter) {
        case 'active':
            customersToShow = customersToShow.filter(c => c.is_active);
            break;
        case 'debts':
            customersToShow = customersToShow.filter(c => c.balance < 0);
            break;
        case 'credits':
            customersToShow = customersToShow.filter(c => c.balance > 0);
            break;
    }

    displayCustomers(customersToShow);
}

// تحديث العملاء
function refreshCustomers() {
    loadCustomersPage();
    showAlert('تم تحديث بيانات العملاء', 'success');
}

// إظهار مودال إضافة عميل
function showAddCustomerModal() {
    document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customerId').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('customerModal'));
    modal.show();
}

// تعديل عميل
function editCustomer(customerId) {
    const customer = allCustomers.find(c => c.id === customerId);
    if (!customer) return;

    document.getElementById('customerModalTitle').textContent = 'تعديل بيانات العميل';
    document.getElementById('customerId').value = customer.id;
    document.getElementById('customerName').value = customer.name;
    document.getElementById('customerPhone').value = customer.phone || '';
    document.getElementById('customerEmail').value = customer.email || '';
    document.getElementById('customerAddress').value = customer.address || '';
    document.getElementById('customerCreditLimit').value = customer.credit_limit || 0;

    const modal = new bootstrap.Modal(document.getElementById('customerModal'));
    modal.show();
}

// حفظ العميل
async function saveCustomer() {
    const customerId = document.getElementById('customerId').value;
    const customerData = {
        name: document.getElementById('customerName').value.trim(),
        phone: document.getElementById('customerPhone').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        credit_limit: parseFloat(document.getElementById('customerCreditLimit').value) || 0
    };

    if (!customerData.name) {
        showAlert('يرجى إدخال اسم العميل', 'warning');
        return;
    }

    try {
        const url = customerId ? `api/customers.php/${customerId}` : 'api/customers.php';
        const method = customerId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerData)
        });

        const result = await response.json();

        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('customerModal')).hide();
            showAlert(customerId ? 'تم تحديث بيانات العميل بنجاح' : 'تم إضافة العميل بنجاح', 'success');
            loadCustomersPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ العميل:', error);
        showAlert('خطأ في حفظ بيانات العميل', 'danger');
    }
}

// حذف عميل
async function deleteCustomer(customerId) {
    const customer = allCustomers.find(c => c.id === customerId);
    if (!customer) return;

    if (!confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
        return;
    }

    try {
        const response = await fetch(`api/customers.php/${customerId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم حذف العميل بنجاح', 'success');
            loadCustomersPage();
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        showAlert('خطأ في حذف العميل', 'danger');
    }
}

// عرض تفاصيل العميل
async function viewCustomerDetails(customerId) {
    try {
        const response = await fetch(`api/customers.php/${customerId}`);
        const result = await response.json();

        if (result.success) {
            const customer = result.data;
            currentCustomer = customer;

            const balanceResponse = await fetch(`api/customers.php/${customerId}/balance`);
            const balanceResult = await balanceResponse.json();

            let balanceInfo = '';
            if (balanceResult.success) {
                const balance = balanceResult.data;
                balanceInfo = `
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">إجمالي المشتريات الآجلة</h6>
                                    <h5 class="text-primary">${balance.total_credit.toFixed(2)} ر.س</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">إجمالي المدفوعات</h6>
                                    <h5 class="text-success">${balance.total_paid.toFixed(2)} ر.س</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">الرصيد الحالي</h6>
                                    <h5 class="${balance.balance >= 0 ? 'text-success' : 'text-danger'}">${balance.balance.toFixed(2)} ر.س</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            const content = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6>معلومات العميل:</h6>
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        <p><strong>رقم الجوال:</strong> ${customer.phone || '-'}</p>
                        <p><strong>البريد الإلكتروني:</strong> ${customer.email || '-'}</p>
                        <p><strong>العنوان:</strong> ${customer.address || '-'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات إضافية:</h6>
                        <p><strong>حد الائتمان:</strong> ${customer.credit_limit || 0} ر.س</p>
                        <p><strong>تاريخ التسجيل:</strong> ${new Date(customer.created_at).toLocaleDateString('ar-SA')}</p>
                        <p><strong>آخر عملية شراء:</strong> ${customer.last_purchase ? new Date(customer.last_purchase).toLocaleDateString('ar-SA') : '-'}</p>
                        <p><strong>الحالة:</strong> ${customer.is_active ? 'نشط' : 'غير نشط'}</p>
                    </div>
                </div>
                ${balanceInfo}
            `;

            document.getElementById('customerDetailsContent').innerHTML = content;
            const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
            modal.show();
        } else {
            showAlert('خطأ في تحميل تفاصيل العميل', 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل تفاصيل العميل:', error);
        showAlert('خطأ في تحميل تفاصيل العميل', 'danger');
    }
}

// إظهار مودال إضافة دفعة
function showAddPaymentModal(customerId = null) {
    if (customerId) {
        document.getElementById('paymentCustomerId').value = customerId;
    } else if (currentCustomer) {
        document.getElementById('paymentCustomerId').value = currentCustomer.id;
    } else {
        showAlert('يرجى تحديد العميل أولاً', 'warning');
        return;
    }

    document.getElementById('paymentForm').reset();
    document.getElementById('paymentCustomerId').value = customerId || currentCustomer.id;

    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// حفظ الدفعة
async function savePayment() {
    const paymentData = {
        customer_id: parseInt(document.getElementById('paymentCustomerId').value),
        amount: parseFloat(document.getElementById('paymentAmount').value),
        payment_method: document.getElementById('paymentMethodSelect').value,
        reference_number: document.getElementById('paymentReference').value.trim(),
        notes: document.getElementById('paymentNotes').value.trim(),
        user_id: 1 // افتراضياً
    };

    if (!paymentData.amount || paymentData.amount <= 0) {
        showAlert('يرجى إدخال مبلغ صحيح', 'warning');
        return;
    }

    try {
        const response = await fetch('api/customers.php/payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(paymentData)
        });

        const result = await response.json();

        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
            showAlert('تم تسجيل الدفعة بنجاح', 'success');
            
            // تحديث البيانات
            loadCustomersPage();
            
            // إذا كان مودال التفاصيل مفتوح، أعد تحميله
            if (currentCustomer) {
                setTimeout(() => viewCustomerDetails(currentCustomer.id), 500);
            }
        } else {
            showAlert('خطأ: ' + result.error, 'danger');
        }
    } catch (error) {
        console.error('خطأ في حفظ الدفعة:', error);
        showAlert('خطأ في حفظ الدفعة', 'danger');
    }
}
