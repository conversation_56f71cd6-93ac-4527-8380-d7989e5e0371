<?php
/**
 * API للتعامل مع فئات المنتجات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

class CategoriesAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    if ($path === '/list') {
                        $this->getCategories();
                    } elseif (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->getCategory($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'POST':
                    $this->createCategory();
                    break;

                case 'PUT':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->updateCategory($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'DELETE':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->deleteCategory($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Server error: ' . $e->getMessage(), 500);
        }
    }

    private function getCategories() {
        $sql = "SELECT * FROM categories WHERE is_active = 1 ORDER BY name";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $categories = $stmt->fetchAll();

        $this->sendSuccess($categories);
    }

    private function getCategory($id) {
        $sql = "SELECT * FROM categories WHERE id = ? AND is_active = 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $category = $stmt->fetch();

        if ($category) {
            $this->sendSuccess($category);
        } else {
            $this->sendError('Category not found', 404);
        }
    }

    private function createCategory() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO categories (name, description) VALUES (?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['description'] ?? null
            ]);

            if ($result) {
                $categoryId = $this->conn->lastInsertId();
                $this->sendSuccess(['id' => $categoryId, 'message' => 'تم إضافة الفئة بنجاح']);
            } else {
                $this->sendError('Failed to create category', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function updateCategory($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "UPDATE categories SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_active = 1";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'] ?? '',
                $input['description'] ?? null,
                $id
            ]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم تحديث الفئة بنجاح']);
            } else {
                $this->sendError('Category not found or no changes made', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function deleteCategory($id) {
        $sql = "UPDATE categories SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف الفئة بنجاح']);
            } else {
                $this->sendError('Category not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
    }
}

$api = new CategoriesAPI();
$api->handleRequest();
?>
