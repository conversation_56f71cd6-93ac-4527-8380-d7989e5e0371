<?php
/**
 * إعدادات قاعدة البيانات لنظام نقاط البيع
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'pos_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function connect() {
        $this->pdo = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
            die();
        }

        return $this->pdo;
    }

    /**
     * إنشاء قاعدة البيانات والجداول
     */
    public function createDatabase() {
        try {
            // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $pdo = new PDO($dsn, $this->username, $this->password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $sql = "CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($sql);
            
            echo "تم إنشاء قاعدة البيانات بنجاح<br>";

            // الاتصال بقاعدة البيانات الجديدة
            $this->pdo = $this->connect();
            
            // إنشاء الجداول
            $this->createTables();
            
        } catch(PDOException $e) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
            die();
        }
    }

    /**
     * إنشاء جداول النظام
     */
    private function createTables() {
        $tables = [
            // جدول المستخدمين
            'users' => "
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    role ENUM('admin', 'manager', 'cashier') DEFAULT 'cashier',
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول العملاء
            'customers' => "
                CREATE TABLE IF NOT EXISTS customers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    credit_limit DECIMAL(10,2) DEFAULT 0.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الموردين
            'suppliers' => "
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    contact_person VARCHAR(100),
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول فئات المنتجات
            'categories' => "
                CREATE TABLE IF NOT EXISTS categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول المنتجات
            'products' => "
                CREATE TABLE IF NOT EXISTS products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    barcode VARCHAR(50) UNIQUE,
                    category_id INT,
                    supplier_id INT,
                    cost_price DECIMAL(10,2) NOT NULL,
                    selling_price DECIMAL(10,2) NOT NULL,
                    stock_quantity INT DEFAULT 0,
                    min_stock_level INT DEFAULT 0,
                    unit VARCHAR(20) DEFAULT 'قطعة',
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الفواتير
            'invoices' => "
                CREATE TABLE IF NOT EXISTS invoices (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_number VARCHAR(50) UNIQUE NOT NULL,
                    customer_id INT,
                    user_id INT NOT NULL,
                    subtotal DECIMAL(10,2) NOT NULL,
                    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
                    discount_amount DECIMAL(10,2) DEFAULT 0.00,
                    total_amount DECIMAL(10,2) NOT NULL,
                    payment_method ENUM('cash', 'credit', 'card') DEFAULT 'cash',
                    payment_status ENUM('paid', 'pending', 'partial') DEFAULT 'paid',
                    paid_amount DECIMAL(10,2) DEFAULT 0.00,
                    status ENUM('completed', 'held', 'cancelled') DEFAULT 'completed',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول عناصر الفواتير
            'invoice_items' => "
                CREATE TABLE IF NOT EXISTS invoice_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_id INT NOT NULL,
                    product_id INT NOT NULL,
                    product_name VARCHAR(200) NOT NULL,
                    quantity DECIMAL(10,3) NOT NULL,
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الفواتير المعلقة
            'held_invoices' => "
                CREATE TABLE IF NOT EXISTS held_invoices (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_data JSON NOT NULL,
                    user_id INT NOT NULL,
                    customer_id INT,
                    total_amount DECIMAL(10,2) NOT NULL,
                    items_count INT NOT NULL,
                    held_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول المدفوعات
            'payments' => "
                CREATE TABLE IF NOT EXISTS payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_id INT,
                    customer_id INT,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash',
                    reference_number VARCHAR(100),
                    notes TEXT,
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE SET NULL,
                    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول حركات المخزون
            'stock_movements' => "
                CREATE TABLE IF NOT EXISTS stock_movements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
                    quantity DECIMAL(10,3) NOT NULL,
                    reference_type ENUM('invoice', 'purchase', 'adjustment', 'return') NOT NULL,
                    reference_id INT,
                    notes TEXT,
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->pdo->exec($sql);
                echo "تم إنشاء جدول {$tableName} بنجاح<br>";
            } catch(PDOException $e) {
                echo "خطأ في إنشاء جدول {$tableName}: " . $e->getMessage() . "<br>";
            }
        }

        // إدراج البيانات الأولية
        $this->insertInitialData();
    }

    /**
     * إدراج البيانات الأولية
     */
    private function insertInitialData() {
        try {
            // إدراج مستخدم افتراضي (مدير)
            $sql = "INSERT IGNORE INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['admin', password_hash('admin123', PASSWORD_DEFAULT), 'المدير العام', 'admin']);

            // إدراج عميل نقدي افتراضي
            $sql = "INSERT IGNORE INTO customers (id, name, phone) VALUES (?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([1, 'عميل نقدي', '']);

            // إدراج فئة افتراضية
            $sql = "INSERT IGNORE INTO categories (name, description) VALUES (?, ?)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['عام', 'فئة عامة للمنتجات']);

            // إدراج منتجات تجريبية
            $products = [
                ['منتج تجريبي 1', '1234567890', 1, 20.00, 25.50, 100],
                ['منتج تجريبي 2', '1234567891', 1, 12.00, 15.75, 50],
                ['منتج تجريبي 3', '1234567892', 1, 28.00, 35.00, 75],
                ['منتج تجريبي 4', '1234567893', 1, 9.00, 12.25, 200],
                ['منتج تجريبي 5', '1234567894', 1, 36.00, 45.80, 30]
            ];

            $sql = "INSERT IGNORE INTO products (name, barcode, category_id, cost_price, selling_price, stock_quantity) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            
            foreach ($products as $product) {
                $stmt->execute($product);
            }

            echo "تم إدراج البيانات الأولية بنجاح<br>";

        } catch(PDOException $e) {
            echo "خطأ في إدراج البيانات الأولية: " . $e->getMessage() . "<br>";
        }
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public function testConnection() {
        try {
            $pdo = $this->connect();
            if ($pdo) {
                echo "تم الاتصال بقاعدة البيانات بنجاح!<br>";
                return true;
            }
        } catch(Exception $e) {
            echo "فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
            return false;
        }
    }
}
?>
