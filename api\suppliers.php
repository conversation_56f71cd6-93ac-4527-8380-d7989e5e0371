<?php
/**
 * API للتعامل مع الموردين
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

class SuppliersAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? '';

        try {
            switch ($method) {
                case 'GET':
                    if ($path === '/list') {
                        $this->getSuppliers();
                    } elseif ($path === '/search') {
                        $this->searchSuppliers();
                    } elseif (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->getSupplier($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'POST':
                    $this->createSupplier();
                    break;

                case 'PUT':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->updateSupplier($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                case 'DELETE':
                    if (preg_match('/\/(\d+)/', $path, $matches)) {
                        $this->deleteSupplier($matches[1]);
                    } else {
                        $this->sendError('Invalid endpoint', 404);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Server error: ' . $e->getMessage(), 500);
        }
    }

    private function getSuppliers() {
        $sql = "SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $suppliers = $stmt->fetchAll();

        $this->sendSuccess($suppliers);
    }

    private function searchSuppliers() {
        $search = $_GET['q'] ?? '';
        
        if (empty($search)) {
            $this->getSuppliers();
            return;
        }

        $sql = "SELECT * FROM suppliers 
                WHERE is_active = 1 
                AND (name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ?) 
                ORDER BY name";

        $searchTerm = '%' . $search . '%';
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        $suppliers = $stmt->fetchAll();

        $this->sendSuccess($suppliers);
    }

    private function getSupplier($id) {
        $sql = "SELECT * FROM suppliers WHERE id = ? AND is_active = 1";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        $supplier = $stmt->fetch();

        if ($supplier) {
            $this->sendSuccess($supplier);
        } else {
            $this->sendError('Supplier not found', 404);
        }
    }

    private function createSupplier() {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name'])) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "INSERT INTO suppliers (name, contact_person, phone, email, address) 
                VALUES (?, ?, ?, ?, ?)";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'],
                $input['contact_person'] ?? null,
                $input['phone'] ?? null,
                $input['email'] ?? null,
                $input['address'] ?? null
            ]);

            if ($result) {
                $supplierId = $this->conn->lastInsertId();
                $this->sendSuccess(['id' => $supplierId, 'message' => 'تم إضافة المورد بنجاح']);
            } else {
                $this->sendError('Failed to create supplier', 500);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function updateSupplier($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            $this->sendError('Invalid input data', 400);
            return;
        }

        $sql = "UPDATE suppliers SET 
                name = ?, contact_person = ?, phone = ?, email = ?, 
                address = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND is_active = 1";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $input['name'] ?? '',
                $input['contact_person'] ?? null,
                $input['phone'] ?? null,
                $input['email'] ?? null,
                $input['address'] ?? null,
                $id
            ]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم تحديث المورد بنجاح']);
            } else {
                $this->sendError('Supplier not found or no changes made', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function deleteSupplier($id) {
        $sql = "UPDATE suppliers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->sendSuccess(['message' => 'تم حذف المورد بنجاح']);
            } else {
                $this->sendError('Supplier not found', 404);
            }

        } catch (PDOException $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }

    private function sendSuccess($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
    }
}

$api = new SuppliersAPI();
$api->handleRequest();
?>
