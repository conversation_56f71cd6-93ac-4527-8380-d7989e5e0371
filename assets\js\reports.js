/**
 * التقارير والإحصائيات - JavaScript
 */

let salesChart = null;
let paymentChart = null;

// تحميل التقارير عند فتح الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash === '#reports' || document.getElementById('reports-page').style.display !== 'none') {
        loadReportsPage();
    }
});

// تحميل صفحة التقارير
async function loadReportsPage() {
    setDefaultDates();
    await loadDashboardStats();
    initializeCharts();
    generateReport();
}

// تعيين التواريخ الافتراضية
function setDefaultDates() {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('report-from-date').value = weekAgo.toISOString().split('T')[0];
    document.getElementById('report-to-date').value = today.toISOString().split('T')[0];
}

// تحميل إحصائيات لوحة التحكم
async function loadDashboardStats() {
    try {
        const today = new Date().toISOString().split('T')[0];
        
        // محاكاة بيانات الإحصائيات
        const stats = {
            todaySales: 2450.75,
            cashSales: 1850.50,
            creditSales: 600.25,
            invoicesCount: 15
        };

        document.getElementById('today-sales').textContent = stats.todaySales.toFixed(2);
        document.getElementById('cash-sales').textContent = stats.cashSales.toFixed(2);
        document.getElementById('credit-sales').textContent = stats.creditSales.toFixed(2);
        document.getElementById('invoices-count').textContent = stats.invoicesCount;

    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
        showAlert('خطأ في تحميل الإحصائيات', 'danger');
    }
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    initializeSalesChart();
    initializePaymentChart();
}

// تهيئة رسم المبيعات
function initializeSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    // بيانات تجريبية لآخر 7 أيام
    const labels = [];
    const data = [];
    
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('ar-SA', { weekday: 'short', month: 'short', day: 'numeric' }));
        data.push(Math.random() * 3000 + 1000); // بيانات عشوائية
    }

    if (salesChart) {
        salesChart.destroy();
    }

    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'المبيعات (ر.س)',
                data: data,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'مبيعات آخر 7 أيام'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(0) + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

// تهيئة رسم طرق الدفع
function initializePaymentChart() {
    const ctx = document.getElementById('paymentChart').getContext('2d');
    
    if (paymentChart) {
        paymentChart.destroy();
    }

    paymentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['نقدي', 'آجل', 'بطاقة ائتمان'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 99, 132)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع طرق الدفع'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// إنشاء التقرير
async function generateReport() {
    const reportType = document.getElementById('report-type').value;
    const fromDate = document.getElementById('report-from-date').value;
    const toDate = document.getElementById('report-to-date').value;

    if (!fromDate || !toDate) {
        showAlert('يرجى تحديد فترة التقرير', 'warning');
        return;
    }

    try {
        switch (reportType) {
            case 'sales':
                await generateSalesReport(fromDate, toDate);
                break;
            case 'customers':
                await generateCustomersReport(fromDate, toDate);
                break;
            case 'products':
                await generateProductsReport(fromDate, toDate);
                break;
            case 'inventory':
                await generateInventoryReport();
                break;
            default:
                showAlert('نوع التقرير غير صحيح', 'warning');
        }
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        showAlert('خطأ في إنشاء التقرير', 'danger');
    }
}

// تقرير المبيعات
async function generateSalesReport(fromDate, toDate) {
    document.getElementById('report-title').textContent = 'تقرير المبيعات';
    
    const tableHead = document.getElementById('report-table-head');
    tableHead.innerHTML = `
        <tr>
            <th>رقم الفاتورة</th>
            <th>التاريخ</th>
            <th>العميل</th>
            <th>المبلغ الإجمالي</th>
            <th>طريقة الدفع</th>
            <th>الحالة</th>
        </tr>
    `;

    // بيانات تجريبية
    const salesData = [
        { invoice_id: 'INV-001', date: '2024-01-15', customer: 'أحمد محمد', total: 150.75, payment_method: 'نقدي', status: 'مكتملة' },
        { invoice_id: 'INV-002', date: '2024-01-15', customer: 'فاطمة علي', total: 89.50, payment_method: 'آجل', status: 'معلقة' },
        { invoice_id: 'INV-003', date: '2024-01-14', customer: 'محمد سعد', total: 245.25, payment_method: 'نقدي', status: 'مكتملة' }
    ];

    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';

    salesData.forEach(sale => {
        const statusClass = sale.status === 'مكتملة' ? 'success' : 'warning';
        const row = `
            <tr>
                <td>${sale.invoice_id}</td>
                <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
                <td>${sale.customer}</td>
                <td>${sale.total.toFixed(2)} ر.س</td>
                <td>${sale.payment_method}</td>
                <td><span class="badge bg-${statusClass}">${sale.status}</span></td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

// تقرير العملاء
async function generateCustomersReport(fromDate, toDate) {
    document.getElementById('report-title').textContent = 'تقرير العملاء';
    
    const tableHead = document.getElementById('report-table-head');
    tableHead.innerHTML = `
        <tr>
            <th>اسم العميل</th>
            <th>عدد المشتريات</th>
            <th>إجمالي المشتريات</th>
            <th>الرصيد الحالي</th>
            <th>آخر عملية شراء</th>
        </tr>
    `;

    // بيانات تجريبية
    const customersData = [
        { name: 'أحمد محمد', purchases_count: 5, total_purchases: 750.25, balance: 150.00, last_purchase: '2024-01-15' },
        { name: 'فاطمة علي', purchases_count: 3, total_purchases: 425.75, balance: -75.50, last_purchase: '2024-01-14' },
        { name: 'محمد سعد', purchases_count: 8, total_purchases: 1250.00, balance: 0, last_purchase: '2024-01-13' }
    ];

    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';

    customersData.forEach(customer => {
        const balanceClass = customer.balance > 0 ? 'text-success' : customer.balance < 0 ? 'text-danger' : 'text-muted';
        const row = `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.purchases_count}</td>
                <td>${customer.total_purchases.toFixed(2)} ر.س</td>
                <td class="${balanceClass}">${customer.balance.toFixed(2)} ر.س</td>
                <td>${new Date(customer.last_purchase).toLocaleDateString('ar-SA')}</td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

// تقرير المنتجات
async function generateProductsReport(fromDate, toDate) {
    document.getElementById('report-title').textContent = 'تقرير المنتجات';
    
    const tableHead = document.getElementById('report-table-head');
    tableHead.innerHTML = `
        <tr>
            <th>اسم المنتج</th>
            <th>الكمية المباعة</th>
            <th>إجمالي المبيعات</th>
            <th>متوسط سعر البيع</th>
            <th>الربح</th>
        </tr>
    `;

    // بيانات تجريبية
    const productsData = [
        { name: 'منتج 1', quantity_sold: 25, total_sales: 625.00, avg_price: 25.00, profit: 125.00 },
        { name: 'منتج 2', quantity_sold: 15, total_sales: 236.25, avg_price: 15.75, profit: 56.25 },
        { name: 'منتج 3', quantity_sold: 30, total_sales: 900.00, avg_price: 30.00, profit: 300.00 }
    ];

    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';

    productsData.forEach(product => {
        const row = `
            <tr>
                <td>${product.name}</td>
                <td>${product.quantity_sold}</td>
                <td>${product.total_sales.toFixed(2)} ر.س</td>
                <td>${product.avg_price.toFixed(2)} ر.س</td>
                <td class="text-success">${product.profit.toFixed(2)} ر.س</td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

// تقرير المخزون
async function generateInventoryReport() {
    document.getElementById('report-title').textContent = 'تقرير المخزون';
    
    const tableHead = document.getElementById('report-table-head');
    tableHead.innerHTML = `
        <tr>
            <th>اسم المنتج</th>
            <th>الكمية المتاحة</th>
            <th>الحد الأدنى</th>
            <th>قيمة المخزون</th>
            <th>الحالة</th>
        </tr>
    `;

    // بيانات تجريبية
    const inventoryData = [
        { name: 'منتج 1', stock: 100, min_stock: 10, value: 2000.00, status: 'متوفر' },
        { name: 'منتج 2', stock: 5, min_stock: 10, value: 60.00, status: 'منخفض' },
        { name: 'منتج 3', stock: 0, min_stock: 5, value: 0, status: 'نفد' }
    ];

    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';

    inventoryData.forEach(item => {
        let statusClass = 'success';
        if (item.status === 'منخفض') statusClass = 'warning';
        if (item.status === 'نفد') statusClass = 'danger';

        const row = `
            <tr>
                <td>${item.name}</td>
                <td>${item.stock}</td>
                <td>${item.min_stock}</td>
                <td>${item.value.toFixed(2)} ر.س</td>
                <td><span class="badge bg-${statusClass}">${item.status}</span></td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

// تحديث التقارير
function refreshReports() {
    loadReportsPage();
    showAlert('تم تحديث التقارير', 'success');
}

// تصدير التقارير
function exportReports() {
    const reportType = document.getElementById('report-type').value;
    const fromDate = document.getElementById('report-from-date').value;
    const toDate = document.getElementById('report-to-date').value;

    // هنا يمكن إضافة وظيفة التصدير الفعلية
    showAlert(`سيتم تصدير تقرير ${getReportTypeName(reportType)} من ${fromDate} إلى ${toDate}`, 'info');
}

// الحصول على اسم نوع التقرير
function getReportTypeName(type) {
    const types = {
        'sales': 'المبيعات',
        'customers': 'العملاء',
        'products': 'المنتجات',
        'inventory': 'المخزون'
    };
    return types[type] || 'غير محدد';
}
