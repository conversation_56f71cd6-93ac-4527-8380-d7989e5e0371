<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cash-register me-2"></i>
                نظام نقاط البيع
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    مرحباً، <span id="username">المدير</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل خروج
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showPage('pos')">
                                <i class="fas fa-cash-register me-2"></i>
                                نقاط البيع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('customers')">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('suppliers')">
                                <i class="fas fa-truck me-2"></i>
                                الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('reports')">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('inventory')">
                                <i class="fas fa-boxes me-2"></i>
                                المخزون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('settings')">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- شاشة نقاط البيع -->
                <div id="pos-page" class="page-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">نقاط البيع</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-warning" onclick="holdInvoice()" title="تعليق الفاتورة الحالية">
                                    <i class="fas fa-pause me-1"></i>
                                    تعليق الفاتورة
                                </button>
                                <button type="button" class="btn btn-info" onclick="showHeldInvoices()" title="عرض الفواتير المعلقة">
                                    <i class="fas fa-list me-1"></i>
                                    الفواتير المعلقة
                                    <span class="badge bg-light text-dark ms-1" id="held-invoices-count">0</span>
                                </button>
                                <button type="button" class="btn btn-success" onclick="newInvoice()" title="إنشاء فاتورة جديدة">
                                    <i class="fas fa-plus me-1"></i>
                                    فاتورة جديدة
                                </button>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCalculator()" title="آلة حاسبة">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="showShortcuts()" title="اختصارات لوحة المفاتيح">
                                    <i class="fas fa-keyboard"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- منطقة البحث والمنتجات -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>البحث عن المنتجات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" id="product-search" 
                                                   placeholder="ابحث بالاسم أو الباركود..." onkeyup="searchProducts()">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-primary w-100" onclick="searchProducts()">
                                                <i class="fas fa-search me-1"></i>
                                                بحث
                                            </button>
                                        </div>
                                    </div>
                                    <div id="products-grid" class="row">
                                        <!-- المنتجات ستظهر هنا -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة الفاتورة -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between">
                                    <h5>الفاتورة الحالية</h5>
                                    <span class="badge bg-primary" id="invoice-number">فاتورة #1</span>
                                </div>
                                <div class="card-body">
                                    <!-- معلومات العميل -->
                                    <div class="mb-3">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" id="customer-select">
                                            <option value="cash">عميل نقدي</option>
                                        </select>
                                    </div>

                                    <!-- قائمة المنتجات في الفاتورة -->
                                    <div class="invoice-items mb-3">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoice-items">
                                                <!-- عناصر الفاتورة ستظهر هنا -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- الخصم -->
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">خصم (%)</label>
                                            <input type="number" class="form-control" id="discount" 
                                                   value="0" min="0" max="100" onchange="calculateTotal()">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">طريقة الدفع</label>
                                            <select class="form-select" id="payment-method">
                                                <option value="cash">نقدي</option>
                                                <option value="credit">آجل</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- الإجمالي -->
                                    <div class="border-top pt-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="discount-amount">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="total">0.00 ر.س</span>
                                        </div>
                                    </div>

                                    <!-- أزرار العمليات -->
                                    <div class="d-grid gap-2 mt-3">
                                        <button class="btn btn-success" onclick="completeInvoice()">
                                            <i class="fas fa-check me-1"></i>
                                            إتمام البيع
                                        </button>
                                        <button class="btn btn-secondary" onclick="printInvoice()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة العملاء -->
                <div id="customers-page" class="page-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة العملاء</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                    </div>

                    <!-- إحصائيات العملاء -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">إجمالي العملاء</h5>
                                    <h2 class="text-primary" id="total-customers">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">عملاء نشطون</h5>
                                    <h2 class="text-success" id="active-customers">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">إجمالي الديون</h5>
                                    <h2 class="text-warning" id="total-debts">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">متوسط الرصيد</h5>
                                    <h2 class="text-info" id="avg-balance">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- البحث والفلترة -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="customer-search"
                                           placeholder="البحث بالاسم أو رقم الجوال..." onkeyup="searchCustomers()">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="customer-filter" onchange="filterCustomers()">
                                        <option value="all">جميع العملاء</option>
                                        <option value="active">نشطون</option>
                                        <option value="debts">لديهم ديون</option>
                                        <option value="credits">لديهم رصيد</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary w-100" onclick="refreshCustomers()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول العملاء -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة العملاء</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>رقم الجوال</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الرصيد</th>
                                            <th>آخر عملية شراء</th>
                                            <th>الحالة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="customers-table">
                                        <!-- بيانات العملاء ستظهر هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة الموردين -->
                <div id="suppliers-page" class="page-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة الموردين</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-primary" onclick="showAddSupplierModal()">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مورد جديد
                            </button>
                        </div>
                    </div>

                    <!-- إحصائيات الموردين -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">إجمالي الموردين</h5>
                                    <h2 class="text-primary" id="total-suppliers">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">موردين نشطون</h5>
                                    <h2 class="text-success" id="active-suppliers">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">إجمالي المستحقات</h5>
                                    <h2 class="text-info" id="total-supplier-balance">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- البحث والفلترة -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" id="supplier-search"
                                           placeholder="البحث بالاسم أو رقم الجوال..." onkeyup="searchSuppliers()">
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-primary w-100" onclick="refreshSuppliers()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الموردين -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة الموردين</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم المورد</th>
                                            <th>الشخص المسؤول</th>
                                            <th>رقم الجوال</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الرصيد</th>
                                            <th>الحالة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="suppliers-table">
                                        <!-- بيانات الموردين ستظهر هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة التقارير -->
                <div id="reports-page" class="page-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">التقارير والإحصائيات</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-outline-primary" onclick="refreshReports()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    تحديث
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="exportReports()">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center bg-primary text-white">
                                <div class="card-body">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <h5 class="card-title">مبيعات اليوم</h5>
                                    <h2 id="today-sales">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-success text-white">
                                <div class="card-body">
                                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                                    <h5 class="card-title">مبيعات نقدية</h5>
                                    <h2 id="cash-sales">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-warning text-white">
                                <div class="card-body">
                                    <i class="fas fa-credit-card fa-2x mb-2"></i>
                                    <h5 class="card-title">مبيعات آجلة</h5>
                                    <h2 id="credit-sales">0 ر.س</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-info text-white">
                                <div class="card-body">
                                    <i class="fas fa-receipt fa-2x mb-2"></i>
                                    <h5 class="card-title">عدد الفواتير</h5>
                                    <h2 id="invoices-count">0</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر التقارير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>فلاتر التقارير</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="report-from-date">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="report-to-date">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">نوع التقرير</label>
                                    <select class="form-select" id="report-type">
                                        <option value="sales">تقرير المبيعات</option>
                                        <option value="customers">تقرير العملاء</option>
                                        <option value="products">تقرير المنتجات</option>
                                        <option value="inventory">تقرير المخزون</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100" onclick="generateReport()">
                                        <i class="fas fa-chart-bar me-1"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>مبيعات آخر 7 أيام</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="salesChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>توزيع طرق الدفع</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="paymentChart" width="200" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول التقرير -->
                    <div class="card">
                        <div class="card-header">
                            <h5 id="report-title">تقرير المبيعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead id="report-table-head">
                                        <!-- رؤوس الجدول ستتغير حسب نوع التقرير -->
                                    </thead>
                                    <tbody id="report-table-body">
                                        <!-- بيانات التقرير ستظهر هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صفحة إدارة المخزون -->
                <div id="inventory-page" class="page-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">إدارة المخزون</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-primary" onclick="showAddProductModal()">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة منتج
                                </button>
                                <button type="button" class="btn btn-warning" onclick="showLowStockAlert()">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    تنبيهات المخزون
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات المخزون -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">إجمالي المنتجات</h5>
                                    <h2 class="text-primary" id="total-products">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">منتجات متوفرة</h5>
                                    <h2 class="text-success" id="available-products">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">مخزون منخفض</h5>
                                    <h2 class="text-warning" id="low-stock-products">0</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">منتجات نفدت</h5>
                                    <h2 class="text-danger" id="out-of-stock-products">0</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- البحث والفلترة -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="inventory-search"
                                           placeholder="البحث بالاسم أو الباركود..." onkeyup="searchInventory()">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="inventory-filter" onchange="filterInventory()">
                                        <option value="all">جميع المنتجات</option>
                                        <option value="available">متوفرة</option>
                                        <option value="low-stock">مخزون منخفض</option>
                                        <option value="out-of-stock">نفدت</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="category-filter" onchange="filterInventory()">
                                        <option value="all">جميع الفئات</option>
                                        <!-- الفئات ستتم إضافتها ديناميكياً -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" onclick="refreshInventory()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المخزون -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة المنتجات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الباركود</th>
                                            <th>الفئة</th>
                                            <th>سعر التكلفة</th>
                                            <th>سعر البيع</th>
                                            <th>الكمية المتاحة</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحالة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="inventory-table">
                                        <!-- بيانات المخزون ستظهر هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="settings-page" class="page-content" style="display: none;">
                    <h1 class="h2">الإعدادات</h1>
                    <p>صفحة الإعدادات قيد التطوير...</p>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal للفواتير المعلقة -->
    <div class="modal fade" id="heldInvoicesModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الفواتير المعلقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="held-invoices-list">
                        <!-- قائمة الفواتير المعلقة ستظهر هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للآلة الحاسبة -->
    <div class="modal fade" id="calculatorModal" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">آلة حاسبة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="calculator">
                        <input type="text" class="form-control mb-3 text-end" id="calc-display" readonly>
                        <div class="row g-2">
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="clearCalc()">C</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('/')">/</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('*')">×</button></div>
                            <div class="col-3"><button class="btn btn-outline-danger w-100" onclick="backspaceCalc()">⌫</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('7')">7</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('8')">8</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('9')">9</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('-')">-</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('4')">4</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('5')">5</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('6')">6</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('+')">+</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('1')">1</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('2')">2</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('3')">3</button></div>
                            <div class="col-3 row-span-2"><button class="btn btn-success w-100 h-100" onclick="calculateResult()">=</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-6"><button class="btn btn-outline-primary w-100" onclick="calcInput('0')">0</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('.')">.</button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لاختصارات لوحة المفاتيح -->
    <div class="modal fade" id="shortcutsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختصارات لوحة المفاتيح</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>العمليات الأساسية:</h6>
                            <ul class="list-unstyled">
                                <li><kbd>F1</kbd> - فاتورة جديدة</li>
                                <li><kbd>F2</kbd> - تعليق الفاتورة</li>
                                <li><kbd>F3</kbd> - الفواتير المعلقة</li>
                                <li><kbd>F4</kbd> - إتمام البيع</li>
                                <li><kbd>F5</kbd> - طباعة الفاتورة</li>
                                <li><kbd>F9</kbd> - آلة حاسبة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>التنقل والبحث:</h6>
                            <ul class="list-unstyled">
                                <li><kbd>Ctrl + F</kbd> - البحث عن منتج</li>
                                <li><kbd>Enter</kbd> - تنفيذ البحث</li>
                                <li><kbd>Esc</kbd> - إلغاء العملية</li>
                                <li><kbd>Tab</kbd> - الانتقال للحقل التالي</li>
                                <li><kbd>Ctrl + D</kbd> - حذف العنصر المحدد</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة/تعديل عميل -->
    <div class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerModalTitle">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <input type="hidden" id="customerId">
                        <div class="mb-3">
                            <label for="customerName" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="mb-3">
                            <label for="customerPhone" class="form-label">رقم الجوال</label>
                            <input type="tel" class="form-control" id="customerPhone">
                        </div>
                        <div class="mb-3">
                            <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="mb-3">
                            <label for="customerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="customerCreditLimit" class="form-label">حد الائتمان (ر.س)</label>
                            <input type="number" class="form-control" id="customerCreditLimit" min="0" step="0.01" value="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomer()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة/تعديل مورد -->
    <div class="modal fade" id="supplierModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="supplierModalTitle">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="supplierForm">
                        <input type="hidden" id="supplierId">
                        <div class="mb-3">
                            <label for="supplierName" class="form-label">اسم المورد *</label>
                            <input type="text" class="form-control" id="supplierName" required>
                        </div>
                        <div class="mb-3">
                            <label for="supplierContactPerson" class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplierContactPerson">
                        </div>
                        <div class="mb-3">
                            <label for="supplierPhone" class="form-label">رقم الجوال</label>
                            <input type="tel" class="form-control" id="supplierPhone">
                        </div>
                        <div class="mb-3">
                            <label for="supplierEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="supplierEmail">
                        </div>
                        <div class="mb-3">
                            <label for="supplierAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="supplierAddress" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSupplier()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل العميل -->
    <div class="modal fade" id="customerDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل العميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="customerDetailsContent">
                        <!-- تفاصيل العميل ستظهر هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="showAddPaymentModal()">
                        <i class="fas fa-money-bill me-1"></i>
                        إضافة دفعة
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة دفعة -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <input type="hidden" id="paymentCustomerId">
                        <div class="mb-3">
                            <label for="paymentAmount" class="form-label">المبلغ (ر.س) *</label>
                            <input type="number" class="form-control" id="paymentAmount" min="0" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="paymentMethodSelect">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="paymentReference" class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" id="paymentReference">
                        </div>
                        <div class="mb-3">
                            <label for="paymentNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="paymentNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="savePayment()">حفظ الدفعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة/تعديل منتج -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <input type="hidden" id="productId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productName" class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productBarcode" class="form-label">الباركود</label>
                                    <input type="text" class="form-control" id="productBarcode">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCategory" class="form-label">الفئة</label>
                                    <select class="form-select" id="productCategory">
                                        <option value="">اختر الفئة</option>
                                        <!-- الفئات ستتم إضافتها ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productSupplier" class="form-label">المورد</label>
                                    <select class="form-select" id="productSupplier">
                                        <option value="">اختر المورد</option>
                                        <!-- الموردين ستتم إضافتهم ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productCostPrice" class="form-label">سعر التكلفة (ر.س) *</label>
                                    <input type="number" class="form-control" id="productCostPrice" min="0" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productSellingPrice" class="form-label">سعر البيع (ر.س) *</label>
                                    <input type="number" class="form-control" id="productSellingPrice" min="0" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="productStock" class="form-label">الكمية المتاحة</label>
                                    <input type="number" class="form-control" id="productStock" min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="productMinStock" class="form-label">الحد الأدنى</label>
                                    <input type="number" class="form-control" id="productMinStock" min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="productUnit" class="form-label">الوحدة</label>
                                    <select class="form-select" id="productUnit">
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلو">كيلو</option>
                                        <option value="لتر">لتر</option>
                                        <option value="متر">متر</option>
                                        <option value="علبة">علبة</option>
                                        <option value="كرتون">كرتون</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="productDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/customers.js"></script>
    <script src="assets/js/suppliers.js"></script>
    <script src="assets/js/inventory.js"></script>
    <script src="assets/js/reports.js"></script>
</body>
</html>
