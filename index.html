<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cash-register me-2"></i>
                نظام نقاط البيع
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    مرحباً، <span id="username">المدير</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل خروج
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showPage('pos')">
                                <i class="fas fa-cash-register me-2"></i>
                                نقاط البيع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('customers')">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('suppliers')">
                                <i class="fas fa-truck me-2"></i>
                                الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('reports')">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('inventory')">
                                <i class="fas fa-boxes me-2"></i>
                                المخزون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('settings')">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- شاشة نقاط البيع -->
                <div id="pos-page" class="page-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">نقاط البيع</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-warning" onclick="holdInvoice()" title="تعليق الفاتورة الحالية">
                                    <i class="fas fa-pause me-1"></i>
                                    تعليق الفاتورة
                                </button>
                                <button type="button" class="btn btn-info" onclick="showHeldInvoices()" title="عرض الفواتير المعلقة">
                                    <i class="fas fa-list me-1"></i>
                                    الفواتير المعلقة
                                    <span class="badge bg-light text-dark ms-1" id="held-invoices-count">0</span>
                                </button>
                                <button type="button" class="btn btn-success" onclick="newInvoice()" title="إنشاء فاتورة جديدة">
                                    <i class="fas fa-plus me-1"></i>
                                    فاتورة جديدة
                                </button>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCalculator()" title="آلة حاسبة">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="showShortcuts()" title="اختصارات لوحة المفاتيح">
                                    <i class="fas fa-keyboard"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- منطقة البحث والمنتجات -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>البحث عن المنتجات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" id="product-search" 
                                                   placeholder="ابحث بالاسم أو الباركود..." onkeyup="searchProducts()">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-primary w-100" onclick="searchProducts()">
                                                <i class="fas fa-search me-1"></i>
                                                بحث
                                            </button>
                                        </div>
                                    </div>
                                    <div id="products-grid" class="row">
                                        <!-- المنتجات ستظهر هنا -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- منطقة الفاتورة -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between">
                                    <h5>الفاتورة الحالية</h5>
                                    <span class="badge bg-primary" id="invoice-number">فاتورة #1</span>
                                </div>
                                <div class="card-body">
                                    <!-- معلومات العميل -->
                                    <div class="mb-3">
                                        <label class="form-label">العميل</label>
                                        <select class="form-select" id="customer-select">
                                            <option value="cash">عميل نقدي</option>
                                        </select>
                                    </div>

                                    <!-- قائمة المنتجات في الفاتورة -->
                                    <div class="invoice-items mb-3">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoice-items">
                                                <!-- عناصر الفاتورة ستظهر هنا -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- الخصم -->
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">خصم (%)</label>
                                            <input type="number" class="form-control" id="discount" 
                                                   value="0" min="0" max="100" onchange="calculateTotal()">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">طريقة الدفع</label>
                                            <select class="form-select" id="payment-method">
                                                <option value="cash">نقدي</option>
                                                <option value="credit">آجل</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- الإجمالي -->
                                    <div class="border-top pt-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <span id="discount-amount">0.00 ر.س</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="total">0.00 ر.س</span>
                                        </div>
                                    </div>

                                    <!-- أزرار العمليات -->
                                    <div class="d-grid gap-2 mt-3">
                                        <button class="btn btn-success" onclick="completeInvoice()">
                                            <i class="fas fa-check me-1"></i>
                                            إتمام البيع
                                        </button>
                                        <button class="btn btn-secondary" onclick="printInvoice()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصفحات الأخرى ستكون مخفية في البداية -->
                <div id="customers-page" class="page-content" style="display: none;">
                    <h1 class="h2">إدارة العملاء</h1>
                    <p>صفحة إدارة العملاء قيد التطوير...</p>
                </div>

                <div id="suppliers-page" class="page-content" style="display: none;">
                    <h1 class="h2">إدارة الموردين</h1>
                    <p>صفحة إدارة الموردين قيد التطوير...</p>
                </div>

                <div id="reports-page" class="page-content" style="display: none;">
                    <h1 class="h2">التقارير</h1>
                    <p>صفحة التقارير قيد التطوير...</p>
                </div>

                <div id="inventory-page" class="page-content" style="display: none;">
                    <h1 class="h2">إدارة المخزون</h1>
                    <p>صفحة إدارة المخزون قيد التطوير...</p>
                </div>

                <div id="settings-page" class="page-content" style="display: none;">
                    <h1 class="h2">الإعدادات</h1>
                    <p>صفحة الإعدادات قيد التطوير...</p>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal للفواتير المعلقة -->
    <div class="modal fade" id="heldInvoicesModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الفواتير المعلقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="held-invoices-list">
                        <!-- قائمة الفواتير المعلقة ستظهر هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للآلة الحاسبة -->
    <div class="modal fade" id="calculatorModal" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">آلة حاسبة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="calculator">
                        <input type="text" class="form-control mb-3 text-end" id="calc-display" readonly>
                        <div class="row g-2">
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="clearCalc()">C</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('/')">/</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('*')">×</button></div>
                            <div class="col-3"><button class="btn btn-outline-danger w-100" onclick="backspaceCalc()">⌫</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('7')">7</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('8')">8</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('9')">9</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('-')">-</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('4')">4</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('5')">5</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('6')">6</button></div>
                            <div class="col-3"><button class="btn btn-outline-secondary w-100" onclick="calcInput('+')">+</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('1')">1</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('2')">2</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('3')">3</button></div>
                            <div class="col-3 row-span-2"><button class="btn btn-success w-100 h-100" onclick="calculateResult()">=</button></div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-6"><button class="btn btn-outline-primary w-100" onclick="calcInput('0')">0</button></div>
                            <div class="col-3"><button class="btn btn-outline-primary w-100" onclick="calcInput('.')">.</button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لاختصارات لوحة المفاتيح -->
    <div class="modal fade" id="shortcutsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختصارات لوحة المفاتيح</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>العمليات الأساسية:</h6>
                            <ul class="list-unstyled">
                                <li><kbd>F1</kbd> - فاتورة جديدة</li>
                                <li><kbd>F2</kbd> - تعليق الفاتورة</li>
                                <li><kbd>F3</kbd> - الفواتير المعلقة</li>
                                <li><kbd>F4</kbd> - إتمام البيع</li>
                                <li><kbd>F5</kbd> - طباعة الفاتورة</li>
                                <li><kbd>F9</kbd> - آلة حاسبة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>التنقل والبحث:</h6>
                            <ul class="list-unstyled">
                                <li><kbd>Ctrl + F</kbd> - البحث عن منتج</li>
                                <li><kbd>Enter</kbd> - تنفيذ البحث</li>
                                <li><kbd>Esc</kbd> - إلغاء العملية</li>
                                <li><kbd>Tab</kbd> - الانتقال للحقل التالي</li>
                                <li><kbd>Ctrl + D</kbd> - حذف العنصر المحدد</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
