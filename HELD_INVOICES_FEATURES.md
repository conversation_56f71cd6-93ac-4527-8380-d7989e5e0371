# ميزة تعليق الفاتورة (Hold Invoice) - دليل شامل 📋

## 🌟 نظرة عامة

ميزة تعليق الفاتورة هي إحدى أهم المميزات في نظام نقاط البيع، حيث تسمح للكاشير بإدارة عدة عملاء في نفس الوقت دون فقدان أي بيانات.

## ✨ المميزات الرئيسية

### 🔄 إدارة الفواتير المعلقة
- **تعليق فوري**: حفظ الفاتورة الحالية بجميع تفاصيلها
- **استكمال سهل**: العودة لأي فاتورة معلقة واستكمالها
- **حذف آمن**: إمكانية حذف الفواتير غير المرغوبة
- **عداد مرئي**: عرض عدد الفواتير المعلقة في الواجهة

### 💾 نظام الحفظ المزدوج
- **قاعدة البيانات**: الحفظ الأساسي في MySQL
- **التخزين المحلي**: نسخة احتياطية في المتصفح
- **مزامنة تلقائية**: تحديث البيانات عند استعادة الاتصال

### 🎨 واجهة مستخدم محسنة
- **تصميم عصري**: واجهة جذابة وسهلة الاستخدام
- **مؤشرات بصرية**: ألوان وأيقونات واضحة
- **إشعارات صوتية**: تنبيهات صوتية للعمليات المهمة
- **مؤشرات التحميل**: عرض حالة التحميل بوضوح

## ⌨️ اختصارات لوحة المفاتيح

| المفتاح | الوظيفة | الوصف |
|---------|---------|--------|
| **F1** | فاتورة جديدة | إنشاء فاتورة جديدة فارغة |
| **F2** | تعليق الفاتورة | حفظ الفاتورة الحالية مؤقتاً |
| **F3** | عرض الفواتير المعلقة | فتح قائمة الفواتير المعلقة |
| **F4** | إتمام البيع | إنهاء الفاتورة الحالية |
| **F5** | طباعة الفاتورة | طباعة الفاتورة |
| **F9** | آلة حاسبة | فتح الآلة الحاسبة |
| **Escape** | إغلاق النوافذ | إغلاق النوافذ المنبثقة |

## 🔧 كيفية الاستخدام

### 1. تعليق فاتورة جديدة
```
1. أضف منتجات للفاتورة
2. اختر العميل وطريقة الدفع
3. اضغط "تعليق الفاتورة" أو F2
4. ستظهر رسالة تأكيد وسيتم إنشاء فاتورة جديدة
```

### 2. عرض الفواتير المعلقة
```
1. اضغط "الفواتير المعلقة" أو F3
2. ستظهر قائمة بجميع الفواتير المعلقة
3. كل فاتورة تعرض: الرقم، العميل، العناصر، الإجمالي، التاريخ
```

### 3. استكمال فاتورة معلقة
```
1. افتح قائمة الفواتير المعلقة (F3)
2. اضغط "استكمال" للفاتورة المطلوبة
3. ستعود جميع البيانات للواجهة
4. يمكنك إضافة أو تعديل المنتجات
5. أكمل البيع كالمعتاد
```

### 4. حذف فاتورة معلقة
```
1. افتح قائمة الفواتير المعلقة (F3)
2. اضغط "حذف" للفاتورة المطلوبة
3. أكد الحذف في النافذة المنبثقة
4. ستتم إزالة الفاتورة نهائياً
```

## 🗃️ هيكل البيانات

### جدول held_invoices
```sql
CREATE TABLE held_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    customer_id INT,
    total_amount DECIMAL(10,2),
    invoice_data JSON,
    held_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

### بيانات الفاتورة المحفوظة
```json
{
    "id": "INV-20241222-001",
    "items": [
        {
            "id": 1,
            "name": "منتج 1",
            "price": 10.00,
            "quantity": 2,
            "total": 20.00
        }
    ],
    "customer": "cash",
    "paymentMethod": "cash",
    "discount": 0,
    "total": 20.00,
    "heldAt": "2024-12-22T10:30:00"
}
```

## 🎯 المميزات المتقدمة

### 🔔 الإشعارات الصوتية
- **تعليق الفاتورة**: صوت تنبيه متوسط
- **استكمال الفاتورة**: صوت نجاح مرتفع
- **حذف الفاتورة**: صوت تحذير منخفض

### 🎨 العداد الذكي
- **أخضر**: لا توجد فواتير معلقة
- **أصفر**: 1-5 فواتير معلقة
- **أحمر**: أكثر من 5 فواتير معلقة

### 📱 التصميم المتجاوب
- **الهواتف**: واجهة محسنة للشاشات الصغيرة
- **الأجهزة اللوحية**: تخطيط متوسط
- **أجهزة الكمبيوتر**: واجهة كاملة

## 🔧 API المتاح

### GET /api/held_invoices.php
```
الحصول على جميع الفواتير المعلقة للمستخدم
المعاملات: user_id
الاستجابة: مصفوفة من الفواتير المعلقة
```

### POST /api/held_invoices.php
```
إنشاء فاتورة معلقة جديدة
البيانات: user_id, customer_id, total_amount, invoice_data
الاستجابة: معرف الفاتورة المعلقة الجديدة
```

### DELETE /api/held_invoices.php/{id}
```
حذف فاتورة معلقة
المعاملات: user_id
الاستجابة: رسالة تأكيد الحذف
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا تظهر الفواتير المعلقة
```
- تحقق من اتصال قاعدة البيانات
- تأكد من إعداد الجداول بشكل صحيح
- تحقق من وحدة التحكم للأخطاء
```

#### 2. العداد لا يتحدث
```
- تحديث الصفحة (F5)
- تحقق من JavaScript في وحدة التحكم
- تأكد من تحميل جميع الملفات
```

#### 3. فشل في حفظ الفاتورة
```
- تحقق من اتصال الإنترنت
- تأكد من تشغيل خادم Apache
- تحقق من صلاحيات قاعدة البيانات
```

#### 4. مشاكل في الاستكمال
```
- تأكد من وجود الفاتورة في قاعدة البيانات
- تحقق من صحة بيانات JSON
- تأكد من تطابق معرف المستخدم
```

## 📊 إحصائيات الأداء

### معايير الأداء المتوقعة
- **زمن التعليق**: أقل من 500ms
- **زمن التحميل**: أقل من 1s
- **زمن الاستكمال**: أقل من 300ms
- **زمن الحذف**: أقل من 200ms

### سعة التخزين
- **قاعدة البيانات**: غير محدودة عملياً
- **التخزين المحلي**: حتى 5MB (حوالي 1000 فاتورة)
- **الذاكرة**: حتى 100 فاتورة معلقة في نفس الوقت

## 🎉 خلاصة

ميزة تعليق الفاتورة تجعل نظام نقاط البيع أكثر مرونة وكفاءة، مما يسمح للكاشير بخدمة عدة عملاء بسرعة ودون أخطاء. النظام مصمم ليكون موثوقاً وسهل الاستخدام مع واجهة عربية كاملة.

---

**تم تطوير هذه الميزة بعناية فائقة لتوفير أفضل تجربة مستخدم ممكنة** ✨
