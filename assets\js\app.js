// متغيرات النظام العامة
let currentInvoice = {
    id: generateInvoiceId(),
    items: [],
    customer: 'cash',
    discount: 0,
    paymentMethod: 'cash',
    total: 0
};

let heldInvoices = JSON.parse(localStorage.getItem('heldInvoices')) || [];
let products = []; // سيتم تحميلها من قاعدة البيانات
let customers = []; // سيتم تحميلها من قاعدة البيانات

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    loadCustomers();
    updateInvoiceDisplay();
    showPage('pos');
});

// إدارة الصفحات
function showPage(pageId) {
    // إخفاء جميع الصفحات
    document.querySelectorAll('.page-content').forEach(page => {
        page.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الروابط
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // إظهار الصفحة المطلوبة
    document.getElementById(pageId + '-page').style.display = 'block';
    
    // إضافة الفئة النشطة للرابط المطلوب
    event.target.classList.add('active');
}

// توليد رقم فاتورة جديد
function generateInvoiceId() {
    return 'INV-' + Date.now();
}

// تحميل المنتجات من قاعدة البيانات
async function loadProducts() {
    try {
        const response = await fetch('api/products.php/list');
        const result = await response.json();

        if (result.success) {
            products = result.data.map(product => ({
                id: product.id,
                name: product.name,
                price: parseFloat(product.selling_price),
                stock: parseInt(product.stock_quantity),
                barcode: product.barcode,
                category: product.category_name,
                unit: product.unit
            }));
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        // استخدام بيانات تجريبية في حالة الخطأ
        products = [
            { id: 1, name: 'منتج تجريبي 1', price: 25.50, stock: 100, barcode: '1234567890' },
            { id: 2, name: 'منتج تجريبي 2', price: 15.75, stock: 50, barcode: '1234567891' },
            { id: 3, name: 'منتج تجريبي 3', price: 35.00, stock: 75, barcode: '1234567892' },
            { id: 4, name: 'منتج تجريبي 4', price: 12.25, stock: 200, barcode: '1234567893' },
            { id: 5, name: 'منتج تجريبي 5', price: 45.80, stock: 30, barcode: '1234567894' }
        ];
    }
    displayProducts(products);
}

// تحميل العملاء من قاعدة البيانات
async function loadCustomers() {
    try {
        const response = await fetch('api/customers.php/list');
        const result = await response.json();

        if (result.success) {
            customers = [
                { id: 'cash', name: 'عميل نقدي', phone: '', balance: 0 },
                ...result.data
            ];
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
        // استخدام بيانات تجريبية في حالة الخطأ
        customers = [
            { id: 'cash', name: 'عميل نقدي', phone: '', balance: 0 },
            { id: 1, name: 'أحمد محمد', phone: '0501234567', balance: 150.00 },
            { id: 2, name: 'فاطمة علي', phone: '0507654321', balance: -75.50 },
            { id: 3, name: 'محمد سعد', phone: '0509876543', balance: 0 }
        ];
    }
    updateCustomerSelect();
}

// تحديث قائمة العملاء
function updateCustomerSelect() {
    const select = document.getElementById('customer-select');
    select.innerHTML = '';
    
    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        if (customer.balance !== 0) {
            option.textContent += ` (الرصيد: ${customer.balance.toFixed(2)} ر.س)`;
        }
        select.appendChild(option);
    });
}

// عرض المنتجات
function displayProducts(productsToShow) {
    const grid = document.getElementById('products-grid');
    grid.innerHTML = '';
    
    productsToShow.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'col-md-6 col-lg-4';
        productCard.innerHTML = `
            <div class="product-card" onclick="addToInvoice(${product.id})">
                <div class="product-name">${product.name}</div>
                <div class="product-price">${product.price.toFixed(2)} ر.س</div>
                <div class="product-stock">المخزون: ${product.stock}</div>
                <div class="text-muted small">الباركود: ${product.barcode}</div>
            </div>
        `;
        grid.appendChild(productCard);
    });
}

// البحث عن المنتجات
async function searchProducts() {
    const searchTerm = document.getElementById('product-search').value.trim();

    if (searchTerm === '') {
        displayProducts(products);
        return;
    }

    try {
        const response = await fetch(`api/products.php/search?q=${encodeURIComponent(searchTerm)}`);
        const result = await response.json();

        if (result.success) {
            const searchResults = result.data.map(product => ({
                id: product.id,
                name: product.name,
                price: parseFloat(product.selling_price),
                stock: parseInt(product.stock_quantity),
                barcode: product.barcode,
                category: product.category_name,
                unit: product.unit
            }));
            displayProducts(searchResults);
        } else {
            // البحث المحلي في حالة فشل API
            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.barcode.includes(searchTerm)
            );
            displayProducts(filteredProducts);
        }
    } catch (error) {
        console.error('خطأ في البحث:', error);
        // البحث المحلي في حالة الخطأ
        const filteredProducts = products.filter(product =>
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.barcode.includes(searchTerm)
        );
        displayProducts(filteredProducts);
    }
}

// إضافة منتج للفاتورة
function addToInvoice(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    if (product.stock <= 0) {
        showAlert('المنتج غير متوفر في المخزون', 'warning');
        return;
    }
    
    const existingItem = currentInvoice.items.find(item => item.productId === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity++;
        } else {
            showAlert('الكمية المطلوبة تتجاوز المخزون المتاح', 'warning');
            return;
        }
    } else {
        currentInvoice.items.push({
            productId: productId,
            name: product.name,
            price: product.price,
            quantity: 1
        });
    }
    
    updateInvoiceDisplay();
}

// إزالة منتج من الفاتورة
function removeFromInvoice(productId) {
    currentInvoice.items = currentInvoice.items.filter(item => item.productId !== productId);
    updateInvoiceDisplay();
}

// تحديث كمية منتج في الفاتورة
function updateQuantity(productId, newQuantity) {
    const item = currentInvoice.items.find(item => item.productId === productId);
    const product = products.find(p => p.id === productId);
    
    if (!item || !product) return;
    
    if (newQuantity <= 0) {
        removeFromInvoice(productId);
        return;
    }
    
    if (newQuantity > product.stock) {
        showAlert('الكمية المطلوبة تتجاوز المخزون المتاح', 'warning');
        return;
    }
    
    item.quantity = newQuantity;
    updateInvoiceDisplay();
}

// تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    const tbody = document.getElementById('invoice-items');
    tbody.innerHTML = '';
    
    currentInvoice.items.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.name}</td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${item.quantity}" min="1" 
                       onchange="updateQuantity(${item.productId}, this.value)">
            </td>
            <td>${(item.price * item.quantity).toFixed(2)} ر.س</td>
            <td>
                <button class="btn btn-danger btn-sm" onclick="removeFromInvoice(${item.productId})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    calculateTotal();
    updateInvoiceNumber();
}

// حساب الإجمالي
function calculateTotal() {
    const subtotal = currentInvoice.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const discountAmount = (subtotal * discount) / 100;
    const total = subtotal - discountAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('discount-amount').textContent = discountAmount.toFixed(2) + ' ر.س';
    document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';
    
    currentInvoice.total = total;
    currentInvoice.discount = discount;
}

// تحديث رقم الفاتورة
function updateInvoiceNumber() {
    document.getElementById('invoice-number').textContent = `فاتورة #${currentInvoice.id}`;
}

// تعليق الفاتورة (Hold Invoice)
function holdInvoice() {
    if (currentInvoice.items.length === 0) {
        showAlert('لا يمكن تعليق فاتورة فارغة', 'warning');
        return;
    }

    // حفظ بيانات الفاتورة الحالية
    const invoiceToHold = {
        ...currentInvoice,
        customer: document.getElementById('customer-select').value,
        paymentMethod: document.getElementById('payment-method').value,
        heldAt: new Date().toISOString(),
        items: [...currentInvoice.items] // نسخ عميق للعناصر
    };

    // محاولة حفظ في قاعدة البيانات أولاً
    saveHeldInvoiceToDatabase(invoiceToHold)
        .then(() => {
            showAlert('تم تعليق الفاتورة بنجاح', 'success');
            updateHeldInvoicesCount();
            // إنشاء فاتورة جديدة
            newInvoice();
        })
        .catch(() => {
            // في حالة فشل قاعدة البيانات، احفظ محلياً
            heldInvoices.push(invoiceToHold);
            localStorage.setItem('heldInvoices', JSON.stringify(heldInvoices));
            updateHeldInvoicesCount();
            showAlert('تم تعليق الفاتورة محلياً', 'warning');
            newInvoice();
        });
}

// عرض الفواتير المعلقة
function showHeldInvoices() {
    const modal = new bootstrap.Modal(document.getElementById('heldInvoicesModal'));
    const list = document.getElementById('held-invoices-list');
    
    list.innerHTML = '';
    
    if (heldInvoices.length === 0) {
        list.innerHTML = '<p class="text-center text-muted">لا توجد فواتير معلقة</p>';
    } else {
        heldInvoices.forEach((invoice, index) => {
            const customer = customers.find(c => c.id == invoice.customer);
            const heldDate = new Date(invoice.heldAt).toLocaleString('ar-SA');
            
            const invoiceDiv = document.createElement('div');
            invoiceDiv.className = 'held-invoice-item';
            invoiceDiv.innerHTML = `
                <div class="invoice-info">
                    <div>
                        <strong>فاتورة #${invoice.id}</strong>
                        <div class="invoice-details">
                            العميل: ${customer ? customer.name : 'عميل نقدي'} | 
                            العناصر: ${invoice.items.length} | 
                            الإجمالي: ${invoice.total.toFixed(2)} ر.س
                        </div>
                        <div class="invoice-details">
                            تاريخ التعليق: ${heldDate}
                        </div>
                    </div>
                    <div>
                        <button class="btn btn-primary btn-sm me-2" onclick="resumeInvoice(${index})">
                            استكمال
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteHeldInvoice(${index})">
                            حذف
                        </button>
                    </div>
                </div>
            `;
            list.appendChild(invoiceDiv);
        });
    }
    
    modal.show();
}

// استكمال فاتورة معلقة
async function resumeInvoice(index, databaseId = null) {
    const invoice = heldInvoices[index];
    const invoiceData = invoice.invoice_data || invoice;

    // استعادة بيانات الفاتورة
    currentInvoice = {
        ...invoiceData,
        items: [...invoiceData.items] // نسخ عميق للعناصر
    };

    // تحديث الواجهة
    document.getElementById('customer-select').value = invoiceData.customer || invoice.customer_id || 'cash';
    document.getElementById('payment-method').value = invoiceData.paymentMethod || 'cash';
    document.getElementById('discount').value = invoiceData.discount || 0;

    updateInvoiceDisplay();

    // إزالة الفاتورة من قاعدة البيانات أو التخزين المحلي
    try {
        if (databaseId) {
            await deleteHeldInvoiceFromDatabase(databaseId);
        } else {
            // إزالة من التخزين المحلي
            heldInvoices.splice(index, 1);
            localStorage.setItem('heldInvoices', JSON.stringify(heldInvoices));
        }

        // إغلاق المودال
        bootstrap.Modal.getInstance(document.getElementById('heldInvoicesModal')).hide();

        updateHeldInvoicesCount();
        showAlert('تم استكمال الفاتورة المعلقة', 'success');
    } catch (error) {
        showAlert('خطأ في استكمال الفاتورة المعلقة', 'danger');
    }
}

// حذف فاتورة معلقة
async function deleteHeldInvoice(index, databaseId = null) {
    if (!confirm('هل أنت متأكد من حذف هذه الفاتورة المعلقة؟')) {
        return;
    }

    try {
        if (databaseId) {
            await deleteHeldInvoiceFromDatabase(databaseId);
        } else {
            // حذف من التخزين المحلي
            heldInvoices.splice(index, 1);
            localStorage.setItem('heldInvoices', JSON.stringify(heldInvoices));
        }

        showHeldInvoices(); // تحديث القائمة
        updateHeldInvoicesCount();
        showAlert('تم حذف الفاتورة المعلقة', 'info');
    } catch (error) {
        showAlert('خطأ في حذف الفاتورة المعلقة', 'danger');
    }
}

// فاتورة جديدة
function newInvoice() {
    currentInvoice = {
        id: generateInvoiceId(),
        items: [],
        customer: 'cash',
        discount: 0,
        paymentMethod: 'cash',
        total: 0
    };
    
    document.getElementById('customer-select').value = 'cash';
    document.getElementById('payment-method').value = 'cash';
    document.getElementById('discount').value = 0;
    document.getElementById('product-search').value = '';
    
    updateInvoiceDisplay();
    displayProducts(products);
    
    showAlert('تم إنشاء فاتورة جديدة', 'info');
}

// إتمام البيع
function completeInvoice() {
    if (currentInvoice.items.length === 0) {
        showAlert('لا يمكن إتمام فاتورة فارغة', 'warning');
        return;
    }
    
    const customer = document.getElementById('customer-select').value;
    const paymentMethod = document.getElementById('payment-method').value;
    
    // هنا يمكن إضافة منطق حفظ الفاتورة في قاعدة البيانات
    console.log('إتمام الفاتورة:', {
        invoice: currentInvoice,
        customer: customer,
        paymentMethod: paymentMethod
    });
    
    showAlert('تم إتمام البيع بنجاح', 'success');
    
    // إنشاء فاتورة جديدة
    setTimeout(() => {
        newInvoice();
    }, 1500);
}

// طباعة الفاتورة
function printInvoice() {
    if (currentInvoice.items.length === 0) {
        showAlert('لا يمكن طباعة فاتورة فارغة', 'warning');
        return;
    }

    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=300,height=600');
    const invoiceHtml = generateInvoiceHTML();

    printWindow.document.write(invoiceHtml);
    printWindow.document.close();

    // طباعة تلقائية
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };

    showAlert('تم إرسال الفاتورة للطباعة', 'success');
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 70px; left: 20px; z-index: 1050; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // هنا يمكن إضافة منطق تسجيل الخروج
        showAlert('تم تسجيل الخروج', 'info');
    }
}

// وظائف التعامل مع قاعدة البيانات
async function saveHeldInvoiceToDatabase(invoiceData) {
    try {
        const response = await fetch('api/held_invoices.php/hold', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                invoice_data: invoiceData,
                user_id: 1, // افتراضياً
                customer_id: invoiceData.customer !== 'cash' ? invoiceData.customer : null
            })
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error);
        }

        return result.data;
    } catch (error) {
        console.error('خطأ في حفظ الفاتورة المعلقة:', error);
        throw error;
    }
}

async function loadHeldInvoicesFromDatabase() {
    try {
        const response = await fetch('api/held_invoices.php/list?user_id=1');
        const result = await response.json();

        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل الفواتير المعلقة:', error);
        // العودة للتخزين المحلي في حالة الخطأ
        return JSON.parse(localStorage.getItem('heldInvoices')) || [];
    }
}

async function deleteHeldInvoiceFromDatabase(invoiceId) {
    try {
        const response = await fetch(`api/held_invoices.php/${invoiceId}?user_id=1`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error);
        }

        return result.data;
    } catch (error) {
        console.error('خطأ في حذف الفاتورة المعلقة:', error);
        throw error;
    }
}

// تحديث وظيفة عرض الفواتير المعلقة لاستخدام قاعدة البيانات
async function showHeldInvoices() {
    const modal = new bootstrap.Modal(document.getElementById('heldInvoicesModal'));
    const list = document.getElementById('held-invoices-list');

    // عرض مؤشر التحميل
    list.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    modal.show();

    try {
        const heldInvoicesFromDB = await loadHeldInvoicesFromDatabase();
        heldInvoices = heldInvoicesFromDB;

        list.innerHTML = '';

        if (heldInvoices.length === 0) {
            list.innerHTML = '<p class="text-center text-muted">لا توجد فواتير معلقة</p>';
        } else {
            heldInvoices.forEach((invoice, index) => {
                const customer = customers.find(c => c.id == invoice.customer_id) ||
                               customers.find(c => c.id == invoice.invoice_data?.customer);
                const heldDate = new Date(invoice.held_at || invoice.invoice_data?.heldAt).toLocaleString('ar-SA');

                const invoiceData = invoice.invoice_data || invoice;

                const invoiceDiv = document.createElement('div');
                invoiceDiv.className = 'held-invoice-item';
                invoiceDiv.innerHTML = `
                    <div class="invoice-info">
                        <div>
                            <strong>فاتورة #${invoiceData.id}</strong>
                            <div class="invoice-details">
                                العميل: ${customer ? customer.name : 'عميل نقدي'} |
                                العناصر: ${invoiceData.items?.length || 0} |
                                الإجمالي: ${(invoice.total_amount || invoiceData.total || 0).toFixed(2)} ر.س
                            </div>
                            <div class="invoice-details">
                                تاريخ التعليق: ${heldDate}
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-primary btn-sm me-2" onclick="resumeInvoice(${index}, ${invoice.id || 'null'})">
                                استكمال
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteHeldInvoice(${index}, ${invoice.id || 'null'})">
                                حذف
                            </button>
                        </div>
                    </div>
                `;
                list.appendChild(invoiceDiv);
            });
        }
    } catch (error) {
        list.innerHTML = '<div class="alert alert-danger">خطأ في تحميل الفواتير المعلقة</div>';
    }
}

// وظائف الآلة الحاسبة
let calcExpression = '';

function showCalculator() {
    const modal = new bootstrap.Modal(document.getElementById('calculatorModal'));
    modal.show();
    clearCalc();
}

function calcInput(value) {
    calcExpression += value;
    document.getElementById('calc-display').value = calcExpression;
}

function clearCalc() {
    calcExpression = '';
    document.getElementById('calc-display').value = '';
}

function backspaceCalc() {
    calcExpression = calcExpression.slice(0, -1);
    document.getElementById('calc-display').value = calcExpression;
}

function calculateResult() {
    try {
        const result = eval(calcExpression.replace('×', '*'));
        document.getElementById('calc-display').value = result;
        calcExpression = result.toString();
    } catch (error) {
        document.getElementById('calc-display').value = 'خطأ';
        calcExpression = '';
    }
}

// عرض اختصارات لوحة المفاتيح
function showShortcuts() {
    const modal = new bootstrap.Modal(document.getElementById('shortcutsModal'));
    modal.show();
}

// تحديث عداد الفواتير المعلقة
function updateHeldInvoicesCount() {
    const count = heldInvoices.length;
    const badge = document.getElementById('held-invoices-count');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

// معالج اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(event) {
    // منع التنفيذ إذا كان المستخدم يكتب في حقل إدخال
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        if (event.key === 'Enter' && event.target.id === 'product-search') {
            searchProducts();
        }
        return;
    }

    // اختصارات لوحة المفاتيح
    switch(event.key) {
        case 'F1':
            event.preventDefault();
            newInvoice();
            break;
        case 'F2':
            event.preventDefault();
            holdInvoice();
            break;
        case 'F3':
            event.preventDefault();
            showHeldInvoices();
            break;
        case 'F4':
            event.preventDefault();
            completeInvoice();
            break;
        case 'F5':
            event.preventDefault();
            printInvoice();
            break;
        case 'F9':
            event.preventDefault();
            showCalculator();
            break;
        case 'Escape':
            // إغلاق المودالات المفتوحة
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                bootstrap.Modal.getInstance(modal)?.hide();
            });
            break;
    }

    // اختصارات مع Ctrl
    if (event.ctrlKey) {
        switch(event.key) {
            case 'f':
                event.preventDefault();
                document.getElementById('product-search').focus();
                break;
            case 'd':
                event.preventDefault();
                // حذف العنصر المحدد (يمكن تطويره لاحقاً)
                break;
        }
    }
});

// تحديث العداد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateHeldInvoicesCount();
});

// إنشاء HTML للفاتورة
function generateInvoiceHTML() {
    const invoiceDate = new Date().toLocaleDateString('ar-SA');
    const invoiceTime = new Date().toLocaleTimeString('ar-SA');
    const invoiceNumber = currentInvoice.id;
    const customer = customers.find(c => c.id == document.getElementById('customer-select').value);
    const paymentMethod = document.getElementById('payment-method').value;

    let itemsHtml = '';
    currentInvoice.items.forEach(item => {
        itemsHtml += `
            <tr>
                <td style="text-align: right;">${item.name}</td>
                <td style="text-align: center;">${item.quantity}</td>
                <td style="text-align: center;">${item.price.toFixed(2)}</td>
                <td style="text-align: center;">${(item.quantity * item.price).toFixed(2)}</td>
            </tr>
        `;
    });

    const subtotal = currentInvoice.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const discountAmount = (subtotal * currentInvoice.discount) / 100;
    const total = subtotal - discountAmount;

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة مبيعات</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    font-size: 12px;
                    margin: 0;
                    padding: 10px;
                    direction: rtl;
                    text-align: right;
                }
                .header {
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }
                .company-name {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .invoice-info {
                    margin-bottom: 15px;
                }
                .invoice-info div {
                    margin-bottom: 3px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }
                th, td {
                    border: 1px solid #000;
                    padding: 5px;
                    font-size: 11px;
                }
                th {
                    background-color: #f0f0f0;
                    font-weight: bold;
                    text-align: center;
                }
                .totals {
                    border-top: 2px solid #000;
                    padding-top: 10px;
                }
                .totals div {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 3px;
                }
                .total-final {
                    font-weight: bold;
                    font-size: 14px;
                    border-top: 1px solid #000;
                    padding-top: 5px;
                }
                .footer {
                    text-align: center;
                    margin-top: 20px;
                    font-size: 10px;
                    border-top: 1px solid #000;
                    padding-top: 10px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">نظام نقاط البيع</div>
                <div>فاتورة مبيعات</div>
            </div>

            <div class="invoice-info">
                <div><strong>رقم الفاتورة:</strong> ${invoiceNumber}</div>
                <div><strong>التاريخ:</strong> ${invoiceDate}</div>
                <div><strong>الوقت:</strong> ${invoiceTime}</div>
                <div><strong>العميل:</strong> ${customer ? customer.name : 'عميل نقدي'}</div>
                <div><strong>طريقة الدفع:</strong> ${paymentMethod === 'cash' ? 'نقدي' : 'آجل'}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>الصنف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHtml}
                </tbody>
            </table>

            <div class="totals">
                <div>
                    <span>المجموع الفرعي:</span>
                    <span>${subtotal.toFixed(2)} ر.س</span>
                </div>
                ${currentInvoice.discount > 0 ? `
                <div>
                    <span>الخصم (${currentInvoice.discount}%):</span>
                    <span>-${discountAmount.toFixed(2)} ر.س</span>
                </div>
                ` : ''}
                <div class="total-final">
                    <span>المجموع النهائي:</span>
                    <span>${total.toFixed(2)} ر.س</span>
                </div>
            </div>

            <div class="footer">
                <div>شكراً لتعاملكم معنا</div>
                <div>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</div>
            </div>
        </body>
        </html>
    `;
}
